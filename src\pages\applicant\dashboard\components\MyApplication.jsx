import { ChevronDownIcon, FilterIcon, SearchIcon } from 'assets/svg';
import {
  Badge,
  Box,
  Button,
  Flex,
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Portal,
  Text
} from 'common/components';
import { DynamicTable } from 'common/custom-components';
import { ROUTE_URL } from 'common/routeUrls';
import { t } from 'i18next';
import { actions as applicationActions } from 'pages/applicant/application/slice';
import { APPLICATION_STATUS, APPLICATION_STATUS_OPTIONS } from 'pages/common/constant';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getScholarshipTypeDisplayName, getStatusColor } from 'utils/common';

const getMyApplicationsColumns = () => [
  {
    key: 'slNo',
    header: t('slNo'),
    type: 'text'
  },
  {
    key: 'applicationNumber',
    header: t('applicationNumber'),
    type: 'text'
  },
  {
    key: 'studentName',
    header: t('studentName'),
    type: 'text'
  },
  {
    key: 'dateOfBirth',
    header: t('dateOfBirth'),
    type: 'date'
  },
  {
    key: 'contactNumber',
    header: t('contactNumber'),
    type: 'text'
  },
  {
    key: 'scholarshipType',
    header: t('scholarshipType'),
    type: 'custom',
    render: (value) => getScholarshipTypeDisplayName(value)
  },
  {
    key: 'status',
    header: t('status'),
    renderCell: ({ value = '' }) => (
      <Badge
        px={4}
        py={1}
        borderRadius="full"
        bg={getStatusColor(value)}
        fontSize="sm"
        fontWeight="semibold"
      >
        {value || 'NIL'}
      </Badge>
    )
  },
  {
    key: 'action',
    header: t('action'),
    type: 'action',
    actions: (row) => {
      const actions = [];

      if (row.status === APPLICATION_STATUS.DRAFT) {
        actions.push({
          type: 'edit',
          label: t('edit'),
          colorScheme: 'blue',
          variant: 'outline',
          borderRadius: 'full'
        });
      } else {
        // Show View button for non-Draft applications
        actions.push({
          type: 'view',
          label: t('view'),
          variant: 'secondary',
          borderRadius: 'full'
        });
      }

      return actions;
    }
  }
];

const MyApplication = ({
  title = 'myApplications',
  applicationData = [],
  loading = false,
  onSearchChange = () => {},
  onFilterChange = () => {},
  onPageChange = () => {},
  showSearch = true,
  showFilter = true,
  showPagination = true,
  emptyMessage = 'noDataFound',
  searchPlaceholder = 'search',
  filterPlaceholder = 'filter',
  itemsPerPage = 5,
  totalItems = 0,
  containerProps = {},
  tableProps = {}
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const [filterValue, setFilterValue] = useState('');

  const handleAction = (type, row) => {
    dispatch(applicationActions.clearAll());
    if (type === 'edit' && row?.id) {
      navigate(`/ui/${ROUTE_URL.APPLICANT.BASE.APPLICATION}/${row.id}`);
    } else if (type === 'view' && row?.id) {
      navigate(`/ui/${ROUTE_URL.APPLICANT.BASE.APPLICATION_VIEW.replace(':applicationId', row.id)}`);
    }
  };

  const tableColumns = getMyApplicationsColumns();

  const handleSearchChange = (e) => {
    const { value } = e.target;
    setSearchValue(value);
    setCurrentPage(1);
    onSearchChange(value);
  };

  const handleFilterChange = (value) => {
    setFilterValue(value);
    setCurrentPage(1);
    onFilterChange(value);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    onPageChange(page);
  };

  const selectedFilterOption = APPLICATION_STATUS_OPTIONS.find(
    (option) => option.code === filterValue
  );

  return (
    <Box overflow="hidden" {...containerProps}>
      {/* Header Section */}
      <Box py={6}>
        <Flex
          direction={{ base: 'column', md: 'row' }}
          align={{ base: 'flex-start', md: 'center' }}
          justify="space-between"
          gap={4}
        >
          {/* Title */}
          <Text
            fontSize={{ base: 'xl', md: '2xl' }}
            fontWeight="medium"
            color="primary.700"
            textAlign="left"
          >
            {t(title)}
          </Text>

          {/* Controls Section */}
          <Flex
            direction={{ base: 'column', md: 'row' }}
            gap={3}
            align={{ base: 'stretch', md: 'center' }}
            w={{ base: '100%', md: 'auto' }}
          >
            {/* Search Input */}
            {showSearch && (
              <InputGroup maxW={{ base: '100%', md: '400px' }}>
                <InputLeftElement pointerEvents="none" h="56px">
                  <Icon as={SearchIcon} color="gray.400" boxSize={6} />
                </InputLeftElement>
                <Input
                  placeholder={t(searchPlaceholder)}
                  value={searchValue}
                  onChange={handleSearchChange}
                  minW={{ base: '100%', md: '350px' }}
                  h="56px"
                  bg="gray.50"
                  border="1px solid"
                  borderColor="gray.300"
                  borderRadius="8px"
                  fontSize="md"
                  _focus={{
                    bg: 'white',
                    borderColor: 'primary.300',
                    boxShadow: '0 0 0 1px rgba(2, 87, 154, 0.2)'
                  }}
                  _placeholder={{ color: 'gray.500' }}
                />
              </InputGroup>
            )}

            {/* Filter Dropdown */}
            {showFilter && APPLICATION_STATUS_OPTIONS.length > 0 && (
            <InputGroup maxW={{ base: '100%', md: '400px' }}>
              <InputLeftElement pointerEvents="none" h="56px">
                <Icon as={FilterIcon} color="gray.400" boxSize={6} />
              </InputLeftElement>
              <Menu>
                <MenuButton
                  as={Button}
                  rightIcon={<Icon as={ChevronDownIcon} boxSize={5} />}
                  variant="outline"
                  h="56px"
                  w={{ base: '100%', md: '200px' }}
                  bg="white"
                  borderColor="gray.300"
                  fontSize="md"
                  fontWeight="500"
                  pl="44px"
                  textAlign="left"
                  _hover={{ bg: 'gray.50' }}
                  _active={{ bg: 'gray.50' }}
                >
                  <Text fontSize="md" fontWeight="400" color="gray.700" isTruncated>
                    {selectedFilterOption?.name || t(filterPlaceholder)}
                  </Text>
                </MenuButton>
                <Portal>
                  <MenuList>
                    <MenuItem onClick={() => handleFilterChange('')}>
                      <Text fontSize="md">All</Text>
                    </MenuItem>
                    {APPLICATION_STATUS_OPTIONS.map((option) => (
                      <MenuItem
                        key={option.code}
                        onClick={() => handleFilterChange(option.code)}
                      >
                        <Text fontSize="md">{option.name}</Text>
                      </MenuItem>
                    ))}
                  </MenuList>
                </Portal>
              </Menu>
            </InputGroup>
            )}
          </Flex>
        </Flex>
      </Box>

      {/* Content Section */}
      <Box overflow="hidden">
        <DynamicTable
          data={applicationData}
          columns={tableColumns}
          loading={loading}
          emptyMessage={emptyMessage}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          totalItems={totalItems}
          onPageChange={handlePageChange}
          showPagination={showPagination}
          onAction={handleAction}
          containerProps={{
            overflowX: 'auto',
            maxWidth: '100%',
            ...tableProps?.containerProps
          }}
          tableProps={{
            minWidth: '1000px',
            ...tableProps
          }}
        />
      </Box>
    </Box>
  );
};

export default MyApplication;
