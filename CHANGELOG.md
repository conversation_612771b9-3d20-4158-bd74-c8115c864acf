# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### [0.0.51] (2025-08-03)
### Bugs fixed:
- **application-view**: applied date removed and table minwidth reduces to avoid scroll & force refetch added to avoid cached data ([`51ae355`](https://github.com/ksmartikm/norka-frontend-service/commit/51ae355506c6a1b5234b54703eaa3f56f25b9d10)) (by archanaharishikm)

### [0.0.50] (2025-08-03)
### Bugs fixed:
- **dashboard**: table value issue([`8ce0d5b`](https://github.com/ksmartikm/norka-frontend-service/commit/8ce0d5b142b37132cd1a7390cb5365bc6314a178)) (by archanaharishikm)
- **application**: wrong data showing in edit fixed([`a118913`](https://github.com/ksmartikm/norka-frontend-service/commit/

### [0.0.49] (2025-08-02)
### New feature:
- **restriction**: block form update after submit and some UI changes([`76d37c7`](https://github.com/ksmartikm/norka-frontend-service/commit/76d37c74088b531edf3cae73dda7f8c7e4b914bf)) (by prasanth-ikm)

### [0.0.48] (2025-08-02)
### New feature:
- **submit-application**: submit api integration & other payload fixes([`ca463b3`](https://github.com/ksmartikm/norka-frontend-service/commit/ca463b3c48ab35eb13ca4d4cbf28f2a25209cc4e)) (by archanaharishikm)
### Bugs fixed:
- **application**: special character restriction in name and malayalam entry restrict and issue fix([`7833ff4`](https://github.com/ksmartikm/norka-frontend-service/commit/7833ff4022ef10af011ecb51cb05f2b0385aa5b3)) (by archanaharishikm)

### [0.0.47] (2025-08-02)
### Bugs fixed:
- **Document**: Document upload and isDirty added in bank,academic and some common fix in file upload([`dc560d5`](https://github.com/ksmartikm/norka-frontend-service/commit/dc560d5e0aab99e830549862bb4f48f72ff181aa)) (by prasanth-ikm)

### [0.0.46] (2025-08-02)
### New feature:
- **application-view**: application-view updated as a page instead of popup and other fixes([`499a0f6`](https://github.com/ksmartikm/norka-frontend-service/commit/499a0f6617d0ad9844bf8647e19eb0fa6be5e653)) (by archanaharishikm)

### [0.0.45] (2025-08-02)
### New feature:
- **bankdetails**: bankDetails payload updated and branch and bank api integrated([`9b149c0`](https://github.com/ksmartikm/norka-frontend-service/commit/9b149c015575d42babdd319d88c8233e34583202)) (by Akbar Haleel)

### [0.0.44] (2025-08-02)
### Bugs fixed:
- **common**: ui, file accept type, toast, scroll to error field([`42412e5`](https://github.com/ksmartikm/norka-frontend-service/commit/42412e5fc5948aa198279f569b0c6c3f622f40a8)) (by prasanth-ikm)

### [0.0.43] (2025-08-02)
### New feature:
- **bankdetails**: bankDetails payload updated and branch and bank api integrated([`9b149c0`](https://github.com/ksmartikm/norka-frontend-service/commit/9b149c015575d42babdd319d88c8233e34583202)) (by Akbar Haleel)
- **ui**: add Card and Stepper components([`f442716`](https://github.com/ksmartikm/norka-frontend-service/commit/f44271699a1e7590e0ff77a49692e4334098b144)) (by Akbar Haleel)

### [0.0.42]  (2025-08-02)
### New feature:
- **ApplicationView**: Application view helper modifications([`0695aef`](https://github.com/ksmartikm/norka-frontend-service/commit/0695aef00e915cf515e5f20989012ccfc52924c8)) (by archanaharishikm)
### Bugs fixed:
- **fixes**: application details validation & field fixes([`0686909`](https://github.com/ksmartikm/norka-frontend-service/commit/06869095d5543c33a935f74719085e125509f6b0)) (by archanaharishikm)

### [0.0.41]  (2025-08-01)
### New feature:
- **parent details**: layout change([`0695aef`](https://github.com/ksmartikm/norka-frontend-service/commit/0695aef00e915cf515e5f20989012ccfc52924c8)) (by archanaharishikm)

### [0.0.40]  (2025-07-31)
### New feature:
- **complete the user section**: complete the user section([`ccf7353`](https://github.com/ksmartikm/norka-frontend-service/commit/ccf73534f6c580f08212afcc9331872208bdd9fb)) (by Akbar Haleel)

### [0.0.39]  (2025-07-31)
### New feature:
- **document**: document preview in Review page and RP logo change([`ae73d7a`](https://github.com/ksmartikm/norka-frontend-service/commit/ae73d7a1ea2dfeb0095a0bd5c9c6bfc1af320686)) (by prasanth-ikm)
### Bugs fixed:
- **document**: minor fix in document preview and accordion style([`52510c7`](https://github.com/ksmartikm/norka-frontend-service/commit/52510c72003ee74245c3771d87819c758a3394e6)) (by prasanth-ikm)

### [0.0.38]  (2025-07-31)
### New feature:
- **forgot password complted**: forgot password is completed([`f0d6c85`](https://github.com/ksmartikm/norka-frontend-service/commit/f0d6c8527eda9970040cd309a9e5f6682262d8ab)) (by Akbar Haleel)
- **completed user authentication section**: completed user authentication section([`6544ce5`](https://github.com/ksmartikm/norka-frontend-service/commit/6544ce5e73b67ff01b0f98832ee4a273531b9e22)) (by Akbar Haleel)

### [0.0.37]  (2025-07-31)
### New feature:
- **application view**: application view added and response chnges related changes([`9b64452`](https://github.com/ksmartikm/norka-frontend-service/commit/9b6445211ddce12efe897f6c1d52b2b596e1d561)) (by archanaharishikm)

### [0.0.36]  (2025-07-31)
### New feature:
- **document**: applicant document details upload integration with preview([`d6976fc`](https://github.com/ksmartikm/norka-frontend-service/commit/d6976fcaf4301f494359abe95e9af2f9901d6014)) (by prasanth-ikm)

### [0.0.35]  (2025-07-30)
### New feature:
- **dashboard**: encrypted user details & validation updates and bug fix([`29c946f`](https://github.com/ksmartikm/norka-frontend-service/commit/29c946f4a1845897a7bc1ea4fc6454b117cc76fd)) (by archanaharishikm)

### [0.0.34]  (2025-07-30)
### New feature:
- **user data**: user data([`22c3ec7`](https://github.com/ksmartikm/norka-frontend-service/commit/22c3ec790ff8517d7efcfbffa1eb93249a079bd2)) (by archanaharishikm)
- **aadhaar**: aadhaar chnages([`a5fbd94`](https://github.com/ksmartikm/norka-frontend-service/commit/a5fbd94ca4da9753488ea7ccaf1cba23656392ae)) (by archanaharishikm)

### [0.0.33] (2025-07-29)
### New feature:
- **academic detaials and validations are completed**: academic details completed([`a84ca75`](https://github.com/ksmartikm/norka-frontend-service/commit/a84ca75651650a16aab58065e569084882cfe9a7)) (by Akbar Haleel)
- **academic details completed**: academic details completed([`9419f50`](https://github.com/ksmartikm/norka-frontend-service/commit/9419f503ba04657a485315966c196694ec162c82)) (by Akbar Haleel)

### [0.0.32] (2025-07-29)
### Bugs fixed:
- **common**: localization fix and document fix([`c96f016`](https://github.com/ksmartikm/norka-frontend-service/commit/c96f01699df5cc00721d14b76d35b7ca244d19e1)) (by prasanth-ikm)

### [0.0.31] (2025-07-29)
### New feature:
- **aadhaar**: check name as in aadhaar & pass uidvault to the api for all forms with adhaar field([`fb84a35`](https://github.com/ksmartikm/norka-frontend-service/commit/fb84a3535142b65f35511222520b7c266a2e9879)) (by archanaharishikm)
- **doc-preview**: doc-preview added([`80666f2`](https://github.com/ksmartikm/norka-frontend-service/commit/80666f2f29b1b88076725200ece99a350c9383e8)) (by archanaharishikm)

### [0.0.30] (2025-07-28)
### Build fixed:
- **Env**: Dynamic env file settings (by prasanth-ikm)

### [0.0.29] (2025-07-28)
### Bugs fixed:
- **Register**: Auth register page code optimize and the folder structure changes([`eb40cd2`](https://github.com/ksmartikm/norka-frontend-service/commit/eb40cd2ccfd9bfc2ad97450817dda9fc7c62f869)) (by prasanth-ikm)

### [0.0.28] (2025-07-23)
### New feature:
- **otp input**: issue fix([`52caa7d`](https://github.com/ksmartikm/norka-frontend-service/commit/52caa7d0294fdaa0cb851cd16a9281e1c2751606)) (by archanaharishikm)
- **token-expiry**: redirection to login page([`ccef7de`](https://github.com/ksmartikm/norka-frontend-service/commit/ccef7de1ba9b9651486091e1b6fe0cc7e610e235)) (by archanaharishikm)
- **dashboard**: dashboard api integration & fixes for date, radio fields([`5afa2ab`](https://github.com/ksmartikm/norka-frontend-service/commit/5afa2abfeaec362001def570e8745ad67384a6a5)) (by archanaharishikm)
- **consent popup**: adhaar consent popup added([`b7e1609`](https://github.com/ksmartikm/norka-frontend-service/commit/b7e1609860e3ba5245ace882c38130920f861d8e)) (by archanaharishikm)

### [0.0.27] (2025-07-21)
### New feature:
- **review & submit changes**: review & submit changes([`c9ce44f`](https://github.com/ksmartikm/norka-frontend-service/commit/c9ce44f085eadb54172e41e23d69a05f5c790c5e)) (by archanaharishikm)
- **review**: updated the review with dynamic data([`c4f9c96`](https://github.com/ksmartikm/norka-frontend-service/commit/c4f9c9633e9440135dbfca122f13e79a008308fe)) (by archanaharishikm)
- **aadhaar**: component modifications([`6b5b0f3`](https://github.com/ksmartikm/norka-frontend-service/commit/6b5b0f3ae959c0f0a995f40998e2d37f99490191)) (by archanaharishikm)
- **otp input**: custom otp input component([`69ea4c8`](https://github.com/ksmartikm/norka-frontend-service/commit/69ea4c87582cc8d68ce4435c6ed6a343b8ee8030)) (by archanaharishikm)

### [0.0.26] (2025-07-17)
### New feature:
- **completed user authentication section**: completed user authentication section([`6544ce5`](https://github.com/ksmartikm/norka-frontend-service/commit/6544ce5e73b67ff01b0f98832ee4a273531b9e22)) (by Akbar Haleel)

### [0.0.25] (2025-07-17)
### New feature:
- **Document**: Fetch, View, file Full view in popup with toolbar and multiple fetch method implemented([`429d02b`](https://github.com/ksmartikm/norka-frontend-service/commit/429d02b8ebc06f70c99a343fde24267a259f74b4)) (by prasanth-ikm)

### [0.0.24] (2025-07-16)
### Bugs fixed:
- **env**: environment file and string replacer added for deployment([`6af2ca0`](https://github.com/ksmartikm/norka-frontend-service/commit/6af2ca0fff5a062bb8bbf0e46b5d7d8b2c927862)) (by prasanth-ikm)

### [0.0.23] (2025-07-16)
### New feature:
- **application**: update & single get api integration for first 2 forms, master scholarshiptype api integration([`699fed6`](https://github.com/ksmartikm/norka-frontend-service/commit/699fed60e83cdb56f1d6ea8253040008a4641723)) (by archanaharishikm)
- **ui changes**: updated the style based on the new figma update([`86d6f4a`](https://github.com/ksmartikm/norka-frontend-service/commit/86d6f4af8216eb197bbb20c30492a87610c3721f)) (by archanaharishikm)

### [0.0.22] (2025-07-16)
### Bugs fixed:
- **ci**: fix in CI pipeline([`c991b7a`](https://github.com/ksmartikm/norka-frontend-service/commit/c991b7a7602227859899ed8534eebce582127b2e)) (by prasanth-ikm)
- **route**: fix route in vite and main routes([`291f920`](https://github.com/ksmartikm/norka-frontend-service/commit/291f920b2caae6122ab65d6439ff87e72bdcdfdc)) (by prasanth-ikm)

### [0.0.21] (2025-07-11)
### New feature:
- **UI**: New ui design changes as per the figma and other ui fix([`e1beed0`](https://github.com/ksmartikm/norka-frontend-service/commit/e1beed031fabbf8bb94a68142d33c7ba37409ecf)) (by prasanth-ikm)

### [0.0.20] (2025-07-11)
### New feature:
- **applicant**: implement stepper navigation commonly and update API queries; enhance localization strings for better user feedback([`11f71a6`](https://github.com/ksmartikm/norka-frontend-service/commit/11f71a6f3e777744b0ed060587cd4fc95dce2ca3)) (by archanaharishikm)

### [0.0.19] (2025-07-10)
### New feature:
-  **applicant**: Enhance applicant details form with dynamic data fetching and validation improvements([`a162f0b`](https://github.com/ksmartikm/norka-frontend-service/commit/a162f0bd848996852d3d790c239cb327d7b2a596)) (by archanaharishikm)
-  **components**: Add StepperButtons component and integrate into applicant detail forms([`1874144`](https://github.com/ksmartikm/norka-frontend-service/commit/1874144291764fc04f8a3858fb2be4e310deef38)) (by archanaharishikm)

### [0.0.18] (2025-07-09)
### New feature:
- **Documents**: Document upload Ui changes and other UI Fixes([`40ab865`](https://github.com/ksmartikm/norka-frontend-service/commit/40ab865e47867c3c161b64ba3a0a4a32e05f21af)) (by prasanth-ikm)

### [0.0.17] (2025-07-09)
### New feature:
- **PDF view**: implemented the pdf viewer as common component([`8b7162f`](https://github.com/ksmartikm/norka-frontend-service/commit/8b7162f0c2a9e44ee65d26513c1cc06563dda67a)) (by prasanth-ikm)
### Bugs fixed:
- **Common**: style fix in document([`303dcdc`](https://github.com/ksmartikm/norka-frontend-service/commit/303dcdc4cba0886a0e21ade4599bbb3232d839d9)) (by prasanth-ikm)

### [0.0.16] (2025-07-02)
### New feature:
- **completed academicdetails and current course details**: completed academicdetails([`de0cbd8`](https://github.com/ksmartikm/norka-frontend-service/commit/de0cbd86ba621f708be8bec5ed28a37ebe6f6a17)) (by Akbar Haleel)
- **scholar**: Bankdetails component completed([`8e30d41`](https://github.com/ksmartikm/norka-frontend-service/commit/8e30d41763751013b054feef39da108a4aaedc8b)) (by Akbar Haleel)
- **ui**: add Card and Stepper components([`f442716`](https://github.com/ksmartikm/norka-frontend-service/commit/f44271699a1e7590e0ff77a49692e4334098b144)) (by Akbar Haleel)

### [0.0.15] (2025-07-07)
### New feature:
- Add custom alert component with various icons and styles([`8035af9`](https://github.com/ksmartikm/norka-frontend-service/commit/8035af94bd59e95b408b7d1e4ad316843b0d86ce)) (by archanaharishikm)
- **i18n**: add parent/guardian info and documents fields to common localization([`d3454f4`](https://github.com/ksmartikm/norka-frontend-service/commit/d3454f46e2326f56ab0a77a899b23dd0b313c473)) (by archanaharishikm)
- **component**: review & submit component added with custom Priview component([`e261d5a`](https://github.com/ksmartikm/norka-frontend-service/commit/e261d5a2ed5f210e51cf751c2a6cab3731a93b5d)) (by archanaharishikm)

### [0.0.14] (2025-07-04)
### New feature:
- **layout**: Expand and shrink functionality added for sidebar([`26baf3f`](https://github.com/ksmartikm/norka-frontend-service/commit/26baf3fec277aadc22de585e98b219a2fac463e1)) (by archanaharishikm)
- **component**: enhance applicant application forms with responsive design, validation improvements([`339405a`](https://github.com/ksmartikm/norka-frontend-service/commit/339405a7eb9fc3d322ea04e57d6f808456679a68)) (by archanaharishikm)

### [0.0.13] (2025-07-03)
### New feature:
- **Document**: Common document fetch implemented([`b278e2d`](https://github.com/ksmartikm/norka-frontend-service/commit/b278e2d6479cab5ecc40f3c5525a0fc52dc0784a)) (by prasanth-ikm)

### [0.0.12] (2025-07-02)
### New feature:
- **component**: add ApplicantDetails component with form fields and validations([`37f4938`](https://github.com/ksmartikm/norka-frontend-service/commit/37f4938c117c8ad3bb22e81769649eb9cf52e705)) (by archanaharishikm)

### [0.0.11] (2025-07-02)
- **scholar**: complete Signup and Otp components([`f781ffb`](https://github.com/ksmartikm/norka-frontend-service/commit/f781ffb9f9c1d744f18e69db2ebf6109d161fdd8)) (by Akbar Haleel)
- **scholar**: complete Signup and Otp components([`1bbc977`](https://github.com/ksmartikm/norka-frontend-service/commit/1bbc9777418d25b76e20bcc9a4fd9b344f04b229)) (by Akbar Haleel)

### [0.0.10] (2025-07-01)
### New feature:
- **components**: Dashboard, Breadcrumb banner, IconButton, alert added ([`32567e8`](https://github.com/ksmartikm/norka-frontend-service/commit/32567e862433d8ec681144190fba8257565b4891)) (by archanaharishikm)

### [0.0.9] (2025-06-30)
### New feature:
- **Layout**: New Responsive layout design([`7559633`](https://github.com/ksmartikm/norka-frontend-service/commit/75596332785d411cef81b0588dec7d115c7a4ed5)) (by prasanth-ikm)
### Bugs fixed:
- eslint issue and props([`2896e6a`](https://github.com/ksmartikm/norka-frontend-service/commit/2896e6a56e717efd650a389b201fa5f35e85dbd0)) (by prasanth-ikm)

### [0.0.8] (2025-06-30)
### New feature:
- **ui**: add document upload component([`09ee1ad`](https://github.com/ksmartikm/norka-frontend-service/commit/09ee1adbce534f73916bb474b43d15fa0315593c)) (by Akbar Haleel)
- **ui**: add accordion, table, and card components([`d55c541`](https://github.com/ksmartikm/norka-frontend-service/commit/d55c541ed4177630e233647f2f1525f28530b670)) (by 

### [0.0.7] (2025-06-29)
### New feature:
- **Components**: Form Components and other fixes ([`0347d34`](https://github.com/ksmartikm/norka-frontend-service/commit/0347d343dac91adcfe29328b8b6716b64cf30ef3)) (by archanaharishikm)

### [0.0.6] 29-06-2025
### New feature:
- **Components**: Accordion component and other style fix([`e776ec7`](https://github.com/ksmartikm/norka-frontend-service/commit/e776ec700d85e24ba4880043afcbc73d7704997b)) (by prasanth-ikm)

### [0.0.5] 28-06-2025
### Bugs fixed:
- **Component**: Import components from a common folder([`7a28d8f`](https://github.com/ksmartikm/norka-frontend-service/commit/7a28d8f0b1a14324a357bf5eec595b083fc83fa0)) (by prasanth-ikm)

### [0.0.4] (2025-06-27)
### New Component:
- **ui**: add Card and Stepper components([`f8b5570`](https://github.com/ksmartikm/norka-frontend-service/commit/f8b55704f918d025ca19477148aad65c79f9b107)) (by Akbar Haleel)


### [0.0.3] (2025-06-27)
### New feature:
- **theme**: add global Chakra UI theme configuration([`b50b480`](https://github.com/ksmartikm/norka-frontend-service/commit/b50b4808b6cb18523464bce8f50aa6675a8fa75e)) (by archanaharishikm)

### [0.0.2] 27-06-2025
### New feature:
- **Common**: RTK query exception handler, baseurl switch and other basic settings([`4127591`](https://github.com/ksmartikm/norka-frontend-service/commit/41275916980365a528100cad5ac18fff9990f559)) (by prasanth-ikm)
- **RTK**: RTK query slice, action and reducer sample implemented([`4f3dbee`](https://github.com/ksmartikm/norka-frontend-service/commit/4f3dbee902813763f47c5780e405b0faf325c241)) (by prasanth-ikm)

### [0.0.1] 26-06-2025
### New feature:
- **Setup**: Base structure setup with RTK query([`5f40892`](https://github.com/ksmartikm/norka-frontend-service/commit/5f408921b9844bff093730d2aeb1af2ca74130d7)) (by prasanth-ikm)