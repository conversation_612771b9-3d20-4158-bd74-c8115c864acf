import {
  Input,
  InputGroup,
  InputRightElement,
  InputLeftElement
} from 'common/components';
import React from 'react';
import { ML_CHAR } from 'common/regex';
import { useCustomToast } from 'utils/hooks';
import ErrorText from '../error-text';
import FormLabel from '../form-label';

const TextInput = (props) => {
  const {
    name,
    label,
    error,
    disabled,
    readOnly,
    required,
    ellipsis,
    rightContent,
    leftContent,
    value,
    type = 'text',
    ...rest
  } = props;

  const showToast = useCustomToast();

  const handleBeforeInput = (e) => {
    if (ML_CHAR.test(e.data)) {
      showToast('malayalamNotAllowed');
      e.preventDefault();
    }
  };

  const handlePaste = (e) => {
    const pasted = e.clipboardData.getData('text');
    if (ML_CHAR.test(pasted)) {
      showToast('malayalamNotAllowed');
      e.preventDefault();
    }
  };

  return (
    <div className="input__container">
      {label && (
        <FormLabel
          disabled={disabled || readOnly}
          label={label}
          required={required}
          ellipsis={ellipsis}
        />
      )}

      <InputGroup className={leftContent ? 'has-left-icon' : ''}>
        {leftContent && (
          <InputLeftElement
            className="custom-left-input-content"
            pointerEvents="none"
          >
            {leftContent}
          </InputLeftElement>
        )}
        <Input
          className={error && !(disabled || readOnly) ? 'error' : ''}
          variant="unstyled"
          type={type}
          id={name}
          name={name}
          disabled={disabled}
          readOnly={readOnly}
          value={[null, undefined].includes(value) ? '' : value}
          onBeforeInput={handleBeforeInput}
          onPaste={handlePaste}
          {...rest}
        />
        {rightContent && (
          <InputRightElement className="custom-right-input-content">
            {rightContent}
          </InputRightElement>
        )}
      </InputGroup>

      {!(disabled || readOnly) && error && <ErrorText error={error} />}
    </div>
  );
};

export default TextInput;
