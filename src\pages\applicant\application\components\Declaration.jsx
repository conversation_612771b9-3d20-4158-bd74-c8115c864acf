import React from 'react';
import {
  Box,
  FormController,
  Grid,
  GridItem,
  t,
  Text,
  VStack,
  Card,
  CardBody,
  Divider
} from 'common/components';

const Declaration = ({ control, errors }) => {
  return (
    <Card
      bg="primary.50"
      borderColor="primary.200"
      borderWidth="2px"
      shadow="md"
    >
      <CardBody p={{ base: 6, md: 8 }}>
        <VStack spacing={6} align="stretch">
          {/* Header Section */}
          <Box textAlign="center">
            <Text
              fontSize={{ base: 'lg', md: 'xl' }}
              fontWeight="bold"
              color="primary.700"
              mb={2}
            >
              {t('declaration')}
            </Text>
            <Divider borderColor="primary.300" />
          </Box>

          {/* Declaration Content */}
          <Box
            bg="white"
            p={{ base: 4, md: 6 }}
            borderRadius="md"
            border="1px solid"
            borderColor="primary.100"
          >
            <Grid templateColumns="repeat(1, 1fr)">
              <GridItem>
                <FormController
                  type="check"
                  name="scholarshipDeclaration"
                  control={control}
                  errors={errors}
                  label={t('scholarshipDeclarationText')}
                />
              </GridItem>
            </Grid>
          </Box>

          {/* Important Notice */}
          <Box
            bg="orange.50"
            p={4}
            borderRadius="md"
            border="1px solid"
            borderColor="orange.200"
          >
            <Text
              fontSize="sm"
              color="orange.700"
              fontWeight="medium"
              textAlign="center"
            >
              ⚠️ {t('declarationImportantNotice')}
            </Text>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default Declaration;
