import React from 'react';
import {
  Box,
  Grid,
  GridItem,
  Text,
  VStack
} from 'common/components';
import colors from 'theme/foundations/colors';

const PreviewItem = ({
  label,
  value,
  colSpan = [12, 6, 4],
  type = 'field',
  subheadingProps = {},
  labelProps = {},
  valueProps = {}
}) => {
  // Render subheading
  if (type === 'subheading') {
    return (
      <GridItem colSpan={colSpan}>
        <Box pb={{ base: 4, md: 4 }} pt={{ base: 4, md: 4 }}>
          <Text
            fontSize="lg"
            fontWeight="medium"
            color={colors.primary[500]}
            lineHeight="1.3"
            {...subheadingProps}
          >
            {label}
          </Text>
        </Box>
      </GridItem>
    );
  }

  // Render regular field
  return (
    <GridItem colSpan={colSpan}>
      <Box pb={{ base: 4, md: 6 }}>
        <VStack align="start" spacing={2}>
          <Text
            fontSize="sm"
            fontWeight="400"
            color="gray.600"
            lineHeight="1.2"
            letterSpacing="0.025em"
            {...labelProps}
          >
            {label}
          </Text>
          <Box
            borderBottom="1px solid"
            borderColor="gray.200"
            pb={2}
            width="100%"
          >
            <Text
              fontSize="md"
              fontWeight="600"
              color="gray.800"
              lineHeight="1.4"
              wordBreak="break-word"
              {...valueProps}
            >
              {value || '-'}
            </Text>
          </Box>
        </VStack>
      </Box>
    </GridItem>
  );
};

const PreviewSection = ({
  data = [],
  spacing = 6,
  gridTemplateColumns = 'repeat(12, 1fr)',
  defaultColSpan = [12, 6, 4],
  ...rest
}) => {
  return (
    <Box
      px={{ base: 4, md: 6 }}
      py={{ base: 4, md: 6 }}
      bg="white"
      {...rest}
    >
      {data.length > 0 ? (
        <Grid
          templateColumns={gridTemplateColumns}
          gap={{ base: 4, md: spacing }}
          rowGap={{ base: 2, md: 4 }}
        >
          {data.map((item, index) => (
            <PreviewItem
              key={item.key || index}
              label={item.label}
              value={item.value}
              colSpan={item.colSpan || defaultColSpan}
              type={item.type || 'field'}
              subheadingProps={item.subheadingProps || {}}
              labelProps={item.labelProps || {}}
              valueProps={item.valueProps || {}}
            />
          ))}
        </Grid>
      ) : (
        <Box
          textAlign="center"
          py={8}
          color="gray.500"
        >
          <Text fontSize="md">
            No data available
          </Text>
        </Box>
      )}
    </Box>
  );
};

export default PreviewSection;
