import React from 'react';
import {
  Box,
  FormController,
  Grid,
  GridItem,
  TitledCard,
  Text
} from 'common/components';

const Declaration = ({ control, errors }) => {
  return (
    <TitledCard title={t('declaration')}>
      <Box
        mx={{ base: 4, md: 6 }}
        my={{ base: 4, md: 6 }}
        p={{ base: 4, md: 6 }}
        border="1px"
        borderColor="gray.300"
        borderRadius="md"
        bg="gray.50"
      >
        <Text fontWeight="bold" mb={2}>
          {t('declarationTitle', 'Declaration by Applicant')}
        </Text>
        <Text fontSize="sm" color="gray.700" mb={4}>
          {t(
            'scholarshipDeclarationText',
            'I hereby declare that the information furnished above is true to the best of my knowledge and belief, and if any discrepancy is found, I shall be held responsible.'
          )}
        </Text>

        <Grid templateColumns="repeat(1, 1fr)" gap={4}>
          <GridItem>
            <FormController
              type="check"
              name="scholarshipDeclaration"
              control={control}
              errors={errors}
              label={t('iAgree', 'I Agree')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="date"
              name="declarationDate"
              control={control}
              errors={errors}
              label={t('date')}
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default Declaration;
