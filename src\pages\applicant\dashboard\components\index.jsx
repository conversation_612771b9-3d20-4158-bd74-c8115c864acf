import React, { useState } from 'react';
import { useGetApplicationsQuery } from 'pages/applicant/application/api';
import { VStack } from 'common/components';
import { useLocation } from 'react-router-dom';
import NewApplication from 'pages/applicant/new-application/components/NewApplication';
import MyApplication from './MyApplication';

const Dashboard = () => {
  const location = useLocation();

  const ShowNewApplication = location.pathname === '/ui/applicant/dashboard';
  const [queryParams, setQueryParams] = useState({
    page: 0,
    size: 5,
    sort: [],
    search: '',
    status: ''
  });

  const {
    data: { payload: applicationResponse = {} } = {},
    isLoading
  } = useGetApplicationsQuery(queryParams, {
    refetchOnMountOrArgChange: true
  });

  const {
    content: applicationData = [],
    totalElements = 0
  } = applicationResponse;

  const isFilterApplied = queryParams.search || queryParams.status;

  const transformedApplicationData = Array.isArray(applicationData)
    ? applicationData.map((item, index) => ({
      ...item,
      slNo: String((queryParams.page * queryParams.size) + index + 1).padStart(2, '0')
    }))
    : [];

  const handleSearchChange = (search) => {
    setQueryParams((prev) => ({
      ...prev,
      search,
      page: 0
    }));
  };

  const handleFilterChange = (status) => {
    setQueryParams((prev) => ({
      ...prev,
      status,
      page: 0
    }));
  };

  const handlePageChange = (page) => {
    setQueryParams((prev) => ({
      ...prev,
      page: page - 1
    }));
  };

  return (
    <VStack spacing={8} align="stretch">
      {/* My Applications Section */}
      {(transformedApplicationData.length > 0 || !ShowNewApplication || isFilterApplied) && (
      <MyApplication
        applicationData={transformedApplicationData}
        loading={isLoading}
        onSearchChange={handleSearchChange}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        itemsPerPage={queryParams.size}
        totalItems={totalElements}
      />
      )}

      {/* New Applications Section */}
      {ShowNewApplication && (
        <NewApplication />)}
    </VStack>
  );
};

export default Dashboard;
