import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { _ } from 'utils/lodash';
import {
  Box,
  FormController,
  Grid,
  GridItem,
  Heading,
  t,
  TitledCard
} from 'common/components';
import { FormLabel } from 'common/custom-components';
import {
  useFetchAllStateQuery,
  useFetchDistrictQuery,
  useFetchUniversitiesQuery
} from 'pages/common/api';
import { STATE } from 'common/constants';
import {
  CGPA, GRADING_OPTIONS, INSTITUTION_TYPE_OPTIONS, MARKS,
  YEAR_OPTIONS
} from '../constants';

const UndergraduateAcademicDetails = ({ boardOptions, gradeOptions }) => {
  const {
    control,
    watch,
    setValue,
    formState: { errors }
  } = useFormContext();

  // Watched values
  const underGradGradingSystem = watch('underGradGradingSystem') || MARKS;
  const underGradMarksOrScore = watch('underGradMarksOrScore');
  const underGradTotalMarks = watch('underGradTotalMarks');
  const underGradMCgpaScore = watch('underGradMCgpaScore');
  const underGradCcpaScore = watch('underGradCcpaScore');
  const selectedState = watch('underGradStateOfInstitution');

  const raw = _.get(gradeOptions, 'payload', []);
  const GradeOptions = Array.isArray(raw) ? raw : [raw];

  useEffect(() => {
    if (!underGradGradingSystem) {
      setValue('underGradGradingSystem', MARKS);
    }
  }, [underGradGradingSystem, setValue]);

  useEffect(() => {
    if (underGradGradingSystem === MARKS) {
      setValue('underGradMCgpaScore', '');
      setValue('underGradCgpaPercentage', '');
      setValue('underGradCcpaScore', '');
      setValue('underGradCcpaPercentage', '');
    } else if (underGradGradingSystem === CGPA) {
      setValue('underGradMarksOrScore', '');
      setValue('underGradTotalMarks', '');
      setValue('underGradPercentage', '');
    }
  }, [underGradGradingSystem, setValue]);

  useEffect(() => {
    if (underGradGradingSystem === MARKS) {
      const score = parseFloat(underGradMarksOrScore);
      const total = parseFloat(underGradTotalMarks);

      if (!Number.isNaN(score) && !Number.isNaN(total) && total > 0) {
        if (score > total) {
          setValue('underGradPercentage', '');
          return;
        }
        const percentage = (score / total) * 100;
        setValue('underGradPercentage', percentage.toFixed(2));
      } else {
        setValue('underGradPercentage', '');
      }
    } else if (underGradGradingSystem === CGPA) {
      const score = parseFloat(underGradMCgpaScore);

      if (!Number.isNaN(score)) {
        if (score > 10) {
          setValue('underGradPercentage', '');
          setValue('underGradCgpaPercentage', '');
          return;
        }
        const percentage = score * 9.5;
        setValue('underGradPercentage', percentage.toFixed(2));
        setValue('underGradCgpaPercentage', percentage.toFixed(2));
      } else {
        setValue('underGradPercentage', '');
        setValue('underGradCgpaPercentage', '');
      }
    }
  }, [
    underGradGradingSystem,
    underGradMarksOrScore,
    underGradTotalMarks,
    underGradMCgpaScore,
    setValue
  ]);

  // Calculate CCPA percentage
  useEffect(() => {
    const score = parseFloat(underGradCcpaScore);
    if (underGradGradingSystem === CGPA && !Number.isNaN(score)) {
      if (score > 10) {
        setValue('underGradCcpaPercentage', '');
        return;
      }
      const percentage = score * 9.5;
      setValue('underGradCcpaPercentage', percentage.toFixed(2));
    } else {
      setValue('underGradCcpaPercentage', '');
    }
  }, [underGradCcpaScore, underGradGradingSystem, setValue]);

  // API calls
  const { data: districtsData } = useFetchDistrictQuery(selectedState, {
    skip: !selectedState
  });

  const { data: universitiesData } = useFetchUniversitiesQuery(
    selectedState,
    { skip: !selectedState }
  );

  const { data: allStates } = useFetchAllStateQuery();

  return (
    <TitledCard title={t('underGraduationDetails')} mt={8}>
      <Box p={6}>
        <Grid templateColumns="repeat(2, 1fr)" gap={6}>
          {/* Institution Details */}
          <GridItem>
            <FormController
              type="select"
              label={t('stateOfInstitution')}
              name="underGradStateOfInstitution"
              control={control}
              errors={errors}
              options={_.get(allStates, 'payload', [])}
              optionKey="id"
              optionLabel="name"
              required
              placeholder={t('select')}
              onChange={() => {
                setValue('university', '');
                setValue('underGradDistrictOfInstitution', '');
              }}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('university')}
              name="university"
              control={control}
              lngOptions={{ en: 'universityName' }}
              errors={errors}
              options={_.get(universitiesData, 'payload', [])}
              optionKey="id"
              optionLabel="universityName"
              required
              placeholder={t('select')}
              isDisabled={!selectedState}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('institutionType')}
              name="ugInstitutionType"
              control={control}
              errors={errors}
              options={INSTITUTION_TYPE_OPTIONS}
              optionKey="code"
              optionLabel="name"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="text"
              label={t('institutionName')}
              name="institutionName"
              control={control}
              errors={errors}
              placeholder={t('institutionName')}
              required
            />
          </GridItem>

          <GridItem>
            <FormController
              type="text"
              label={t('institutionLocation')}
              name="institutionLocation"
              control={control}
              errors={errors}
              required
              placeholder={t('enterHere')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="underGradDistrictOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtsData, 'payload', [])}
              optionKey="id"
              optionLabel="name"
              required
              placeholder={t('select')}
              isDisabled={!selectedState}
            />
          </GridItem>

          {/* Course Details */}
          <GridItem>
            <FormController
              type="text"
              label={t('courseName')}
              name="underGradCourseName"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('stream')}
              name="underGradStreamCriteria"
              control={control}
              errors={errors}
              options={boardOptions}
              optionKey="id"
              optionLabel="name"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="underGradYearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              optionLabel="name"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="text"
              label={t('registrationNumber')}
              name="registerNumber"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
            />
          </GridItem>

          <FormController
            type="select"
            label={t('grade')}
            name="streamCriteria"
            control={control}
            errors={errors}
            options={GradeOptions}
            optionKey="code"
            optionLabel="name"
            required
            placeholder={t('select')}
          />

          {/* Competitive Exam (conditionally shown) */}
          {selectedState !== STATE.id && (
            <GridItem colSpan={2}>
              <FormController
                type="text"
                label={t('competitiveExamAllIndiaLevel')}
                name="competitiveExam"
                control={control}
                errors={errors}
                placeholder={t('enterHere')}
              />
            </GridItem>
          )}

          {/* Grading System Selection */}
          <GridItem colSpan={2} mt={4}>
            <FormLabel label={t('gradingSystem')} required />
            <Box mt={2} mb={4}>
              <FormController
                type="radio"
                name="underGradGradingSystem"
                control={control}
                errors={errors}
                options={GRADING_OPTIONS}
                optionKey="code"
                optionLabel="name"
                required
                direction="row"
                defaultValue={MARKS}
              />
            </Box>
          </GridItem>

          {/* Marks System */}
          {underGradGradingSystem === MARKS && (
            <GridItem colSpan={2}>
              <Grid templateColumns="repeat(3, 1fr)" gap={4} mt={4}>
                <GridItem>
                  <FormController
                    type="number"
                    label={t('marksObtained')}
                    name="underGradMarksOrScore"
                    control={control}
                    errors={errors}
                    placeholder={t('enterHere')}
                    required
                    min={0}
                  />
                </GridItem>
                <GridItem>
                  <FormController
                    type="number"
                    label={t('totalMarks')}
                    name="underGradTotalMarks"
                    control={control}
                    errors={errors}
                    placeholder={t('enterHere')}
                    required
                    min={1}
                  />
                </GridItem>
                <GridItem>
                  <FormController
                    type="text"
                    label={t('percentage')}
                    name="underGradPercentage"
                    control={control}
                    errors={errors}
                    required
                    isReadOnly
                    bg="gray.100"
                  />
                </GridItem>
              </Grid>
            </GridItem>
          )}

          {/* CGPA System */}
          {underGradGradingSystem === CGPA && (
            <>
              <GridItem colSpan={2}>
                <Box mb={4}>
                  <Heading size="md" mb={3}>
                    {t('cgpaOgpaDetails')}
                  </Heading>
                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <GridItem>
                      <FormController
                        type="number"
                        label={t('score')}
                        name="underGradMCgpaScore"
                        control={control}
                        errors={errors}
                        placeholder={t('enterHere')}
                        required
                        min={0}
                        max={10}
                        step="0.01"
                      />
                    </GridItem>
                    <GridItem>
                      <FormController
                        type="text"
                        label={t('percentage')}
                        name="underGradCgpaPercentage"
                        control={control}
                        errors={errors}
                        required
                        isReadOnly
                        bg="gray.100"
                      />
                    </GridItem>
                  </Grid>
                </Box>
              </GridItem>

              <GridItem colSpan={2}>
                <Box>
                  <Heading size="md" mb={3}>
                    {t('ccpaDetailsIfApplicable')}
                  </Heading>
                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <GridItem>
                      <FormController
                        type="number"
                        label={t('score')}
                        name="underGradCcpaScore"
                        control={control}
                        errors={errors}
                        placeholder={t('enterHere')}
                        min={0}
                        max={10}
                        step="0.01"
                      />
                    </GridItem>
                    <GridItem>
                      <FormController
                        type="text"
                        label={t('percentage')}
                        name="underGradCcpaPercentage"
                        control={control}
                        errors={errors}
                        isReadOnly
                        bg="gray.100"
                      />
                    </GridItem>
                  </Grid>
                </Box>
              </GridItem>
            </>
          )}

        </Grid>
      </Box>
    </TitledCard>
  );
};

export default UndergraduateAcademicDetails;
