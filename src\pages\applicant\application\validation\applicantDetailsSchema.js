import * as yup from 'yup';
import { t } from 'i18next';
import { YES_OR_NO } from 'pages/common/constant';
import {
  AADHAAR, EMAIL, MASKED_AADHAAR, MOBILE, PINCODE
} from 'common/regex';
import { STATE } from 'common/constants';
import { FAMILY_CIRCUMSTANCES } from '../constants';

export const applicantDetailsSchema = yup.object().shape({
  // Personal Details
  firstName: yup
    .string()
    .required(t('fieldRequired', { field: t('firstName') }))
    .min(2, t('mustBeAtLeast', { type: t('firstName'), count: 2, unit: 'characters' }))
    .max(50, t('fieldMaxLength', { field: t('firstName'), max: 50 })),

  middleName: yup
    .string()
    .nullable()
    .notRequired()
    .max(50, t('fieldMaxLength', { field: t('middleName'), max: 50 })),

  lastName: yup
    .string()
    .nullable()
    .notRequired()
    .max(50, t('fieldMaxLength', { field: t('lastName'), max: 50 })),

  dateOfBirth: yup
    .date()
    .required(t('fieldRequired', { field: t('dateOfBirth') }))
    .min(new Date('2000-01-01'), t('fieldMinDate', { field: t('dateOfBirth'), date: '01-01-2000' }))
    .max(new Date(), t('fieldFutureDate', { field: t('dateOfBirth') }))
    .test('age', t('fieldMinAge', { age: 16 }), (value) => {
      if (!value) return false;
      const today = new Date();
      const birthDate = new Date(value);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age -= 1;
      }
      return age >= 16;
    }),

  gender: yup
    .string()
    .required(t('fieldSelectOption', { field: t('gender') })),

  aadhaarNumber: yup
    .string()
    .required(t('fieldRequired', { field: t('aadhaarNumber') }))
    .test(
      'aadhaar-format',
      t('fieldValidFormat', { field: t('aadhaarNumber') }),
      (value) => {
        if (!value) return false;
        return AADHAAR.test(value) || MASKED_AADHAAR.test(value);
      }
    ),

  // Permanent Address
  houseNumber: yup
    .string()
    .required(t('fieldRequired', { field: t('houseNumber') }))
    .max(100, t('fieldMaxLength', { field: t('houseNumber'), max: 100 })),

  streetLocality: yup
    .string()
    .required(t('fieldRequired', { field: t('streetLocality') }))
    .max(200, t('fieldMaxLength', { field: t('streetLocality'), max: 200 })),

  cityTown: yup
    .string()
    .required(t('fieldRequired', { field: t('cityTown') }))
    .max(100, t('fieldMaxLength', { field: t('cityTown'), max: 100 })),

  district: yup
    .string()
    .required(t('fieldSelectOption', { field: t('district') })),

  state: yup
    .string()
    .required(t('fieldSelectOption', { field: t('state') })),

  pincode: yup
    .string()
    .required(t('fieldRequired', { field: t('pincode') }))
    .matches(PINCODE, t('fieldExactLength', { field: t('pincode'), length: 6 })),

  mobileNumber: yup
    .string()
    .transform((value) => (value ? String(value) : ''))
    .required(t('fieldRequired', { field: t('mobileNumber') }))
    .matches(MOBILE, t('fieldValidMobile')),

  emailId: yup
    .string()
    .transform((value) => (value ? String(value) : ''))
    .required(t('fieldRequired', { field: t('emailId') }))
    .test(
      'emailFormat',
      t('fieldValidFormat', { field: t('emailId') }),
      (value) => !value || EMAIL.test(value)
    ),

  // Questions
  isResident: yup
    .string()
    .required(t('fieldSelectOption', { field: t('isResident') })),

  isNriParent: yup
    .string()
    .required(t('fieldSelectOption', { field: t('isNriParent') })),

  pravasiIdCardNumber: yup
    .string()
    .when('isNriParent', {
      is: YES_OR_NO.YES,
      then: (schema) => schema.required(t('fieldRequired', { field: t('pravasiIdCardNumber') })),
      otherwise: (schema) => schema.notRequired()
    }),

  isDifferentlyAbled: yup
    .string()
    .required(t('fieldSelectOption', { field: t('isDifferentlyAbled') })),

  percentageOfDisability: yup
    .number()
    .nullable()
    .transform((value, originalValue) => {
      // Transform empty string to null to avoid NaN
      return originalValue === '' ? null : value;
    })
    .when('isDifferentlyAbled', {
      is: YES_OR_NO.YES,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('percentageOfDisability') }))
        .min(60, t('fieldMinValue', { field: t('percentageOfDisability'), min: 60, unit: '%' }))
        .max(100, t('fieldMaxValue', { field: t('percentageOfDisability'), max: 100 })),
      otherwise: (schema) => schema.notRequired()
    }),

  // Declaration
  scholarshipDeclaration: yup
    .boolean()
    .oneOf([true], t('fieldRequired', { field: t('declaration') }))
});

export const combinedApplicantDetailsSchema = applicantDetailsSchema;

export const APPLICANT_DETAILS_DEFAULT_VALUES = {
  firstName: '',
  middleName: '',
  lastName: '',
  dateOfBirth: null,
  gender: null,
  aadhaarNumber: '',
  houseNumber: '',
  streetLocality: '',
  cityTown: '',
  district: '',
  state: STATE.id,
  pincode: '',
  mobileNumber: '',
  emailId: '',
  isResident: '',
  isNriParent: '',
  pravasiIdCardNumber: '',
  isDifferentlyAbled: '',
  percentageOfDisability: null
};

// Additional Details (If applicable) Default Values
export const ADDITIONAL_DETAILS_DEFAULT_VALUES = {
  familyCircumstances: FAMILY_CIRCUMSTANCES.NOT_APPLICABLE,
  hasRepresentedAtStateLevel: YES_OR_NO.NO,
  scholarshipDeclaration: false
};

export const COMBINED_DEFAULT_VALUES = {
  ...APPLICANT_DETAILS_DEFAULT_VALUES,
  ...ADDITIONAL_DETAILS_DEFAULT_VALUES
};
