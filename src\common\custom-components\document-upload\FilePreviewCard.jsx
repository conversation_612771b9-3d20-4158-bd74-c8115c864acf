import {
  ExpandIcon, PDFIcon, TrashIcon
} from 'assets/svg';
import {
  Box, HStack, IconButton, Image, Text, VStack, Icon, Tooltip, Stack, t
} from 'common/components';

const FileCard = ({ file, handleFileDelete, handleFilePreview }) => {
  const isImage = file.type.startsWith('image/');

  const formatFileSize = (bytes) => {
    if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    }
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };

  return (
    <HStack
      spacing={4}
      p={4}
      borderRadius="md"
      bg="white"
      align="center"
      justify="space-between"
      width="100%"
    >
      <HStack spacing={3}>
        <Box boxSize={{ md: '45px', base: '100%' }}>
          {isImage ? (
            <Image
              src={file.url}
              alt={file.name}
              boxSize="45px"
              borderRadius="sm"
              border="1px"
              borderColor="primary.80"
              objectFit="cover"
            />
          ) : (
            <Icon as={PDFIcon} boxSize={9} color="red.500" />
          )}
          <Box fontSize="xs" color="gray.500" display={{ base: 'block', md: 'none' }}>
            <Text color="gray.500" display="flex" width="80px" whiteSpace="nowrap" overflow="hidden">{file.name}</Text>
            <Text color="gray.500">{file.size ? formatFileSize(file.size) : ''}
              <Text as="span" color="green.500" bg="green.100" px={2} borderRadius="30px">{t('completed')}</Text>
            </Text>
          </Box>
        </Box>

        <VStack align="start" spacing={0} display={{ base: 'none', md: 'flex' }}>
          <Text fontWeight="medium" fontSize="sm">
            {file.name}
          </Text>
          <Text fontSize="xs" color="gray.500" alignItems="center">
            {file.size ? formatFileSize(file.size) : ''}
            <Text as="span" color="green.500" bg="green.100" px={2} borderRadius="30px">● {t('completed')} </Text>
          </Text>
        </VStack>
      </HStack>

      <Stack spacing={2} direction={{ base: 'column', md: 'row' }}>
        <Tooltip label="Preview">
          <IconButton
            icon={<ExpandIcon />}
            size="sm"
            variant="ghost"
            _hover={{ bg: 'white' }}
            aria-label="Preview"
            onClick={() => handleFilePreview(file)}
          />
        </Tooltip>
        <Tooltip label="Delete">
          <IconButton
            icon={<TrashIcon stroke="#C53030" />}
            size="sm"
            variant="ghost"
            aria-label="Delete"
            _hover={{ bg: 'white' }}
            onClick={() => handleFileDelete(file)}
          />
        </Tooltip>
      </Stack>
    </HStack>
  );
};

export default FileCard;
