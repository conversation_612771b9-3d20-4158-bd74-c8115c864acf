import { AADHAAR, EN, NUM_ONLY } from 'common/regex';

/**
 * Utility function to limit the number of digits in a number input field
 * Prevents scientific notation characters (e, E, +, -)
 * @param {number} maxLength - Maximum number of digits allowed
 * @returns {function} - Event handler function for onInput
 */
export const limitInputLength = (maxLength) => {
  return (e) => {
    // Remove scientific notation and non-digit characters
    let value = e.target.value.replace(NUM_ONLY, '');

    if (value.length > maxLength) {
      value = value.slice(0, maxLength);
    }

    e.target.value = value;
  };
};

/**
 * Utility function to limit input length with custom validation
 * Prevents scientific notation characters (e, E, +, -)
 * @param {number} maxLength - Maximum number of digits allowed
 * @param {function} customValidator - Optional custom validation function
 * @returns {function} - Event handler function for onInput
 */
export const limitInputLengthWithValidation = (maxLength, customValidator) => {
  return (e) => {
    let value = e.target.value.replace(NUM_ONLY, '');

    if (value.length > maxLength) {
      value = value.slice(0, maxLength);
    }
    e.target.value = value;

    // Apply custom validation if provided
    if (customValidator && typeof customValidator === 'function') {
      customValidator(e);
    }
  };
};

/**
 * Aadhaar input handler: Enforces maxLength 12 and Aadhaar format
 * Uses exact Aadhaar regex after 12 digits
 * Prevents scientific notation characters (e, E, +, -)
 * @param {React.ChangeEvent<HTMLInputElement>} e
 */
export const aadhaarInputHandler = (e) => {
  // Remove scientific notation and non-digit characters
  let value = e.target.value.replace(NUM_ONLY, '');
  value = value.slice(0, 12);
  if (value.length >= 1 && !/^[2-9]/.test(value)) {
    value = '';
  }
  if (value.length === 12 && !AADHAAR.test(value)) {
    value = '';
  }
  e.target.value = value;
};

/**
 * Name input handler: Enforces only characters matching the EN regex
 * Removes characters that don't match the allowed set
 */
export const nameInputHandler = (e) => {
  const { value } = e.target;

  // Only keep characters that match the EN regex
  if (!EN.test(value)) {
    const allowed = /[A-Za-z.'"\s]/g;
    e.target.value = (value.match(allowed) || []).join('');
  }
};

/**
 * Predefined input handlers for common field types
 */
export const inputHandlers = {
  aadhaar: aadhaarInputHandler,
  mobile: limitInputLength(10),
  pincode: limitInputLength(6),
  percentage: limitInputLength(3),
  name: nameInputHandler,

  // Custom handlers with additional validation
  mobileWithValidation: limitInputLengthWithValidation(10, (e) => {
    // Ensure mobile number starts with 6-9
    const { value } = e.target;
    if (value.length > 0 && !['6', '7', '8', '9'].includes(value[0])) {
      e.target.value = '';
    }
  }),

  percentageWithRange: limitInputLengthWithValidation(3, (e) => {
    // Ensure percentage is between 0-100
    const value = parseInt(e.target.value, 10);
    if (value > 100) {
      e.target.value = '100';
    }
  }),

  annualIncome: limitInputLengthWithValidation(6, (e) => {
    const value = parseInt(e.target.value, 10);
    if (value > 250000) {
      e.target.value = '250000';
    }
  })
};
