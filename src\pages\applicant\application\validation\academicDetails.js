// validation/academicDetails.js
import * as yup from 'yup';
import { t } from 'common/components';

export const class10Schema = yup.object().shape({
  board: yup.string().required(t('fieldRequired', { field: t('board') })),
  institutionType: yup.string().required(t('fieldRequired', { field: t('institutionType') })),
  institutionName: yup.string()
    .required(t('fieldRequired', { field: t('institutionName') }))
    .min(5, t('fieldMinLength', { field: t('institutionName'), min: t('5') })),
  institutionLocation: yup.string()
    .required(t('fieldRequired', { field: t('institutionLocation') }))
    .min(5, t('fieldMinLength', { field: t('institutionLocation'), min: t('5') })),
  state: yup.string().required(t('fieldRequired', { field: t('state') })),
  districtOfInstitution: yup.string().required(t('fieldRequired', { field: t('district') })),
  registerNumber: yup.string()
    .required(t('fieldRequired', { field: t('registerNumber') }))
    .matches(/^[a-zA-Z0-9]+$/, t('onlyLettersAndNumbers')),
  yearOfCompletion: yup.string().required(t('fieldRequired', { field: t('year') })),
  streamCriteria: yup.string().required(t('fieldRequired', { field: t('grade') })),
  marksObtained: yup.string().required('Marks Obtained is a required field'),
  totalMarks: yup.string().required('Total Marks is a required field'),
  academicYear: yup.string().required('Academic Year is a required field'),
  courseMode: yup.string().oneOf(['REGULAR', 'DISTANCE'], 'Course Mode is a required').required('Course Mode is a required'),
  currentInstitutionName: yup.string().required('Institution Name is a required field'),
  currentInstitutionType: yup.string().oneOf(['Government', 'AIDED', 'PRIVATE'], 'InstitutionType is a required field').required('InstitutionType is a required field'),
  dateOfAdmission: yup
    .date()
    .transform((value, originalValue) => (originalValue === '' ? undefined : value))
    .required('Date of Admission is required')
});

export const higherSecondarySchema = yup.object().shape({
  board: yup.string().required(t('fieldRequired', { field: t('board') })),
  hsInstitutionType: yup.string().required(t('fieldRequired', { field: t('institutionType') })),
  hsInstitutionName: yup.string()
    .required(t('fieldRequired', { field: t('institutionName') }))
    .min(5, t('fieldMinLength', { field: t('institutionName'), min: t('5') })),
  hsInstitutionLocation: yup.string()
    .required(t('fieldRequired', { field: t('institutionLocation') }))
    .min(5, t('fieldMinLength', { field: t('institutionLocation'), min: t('5') })),
  hsState: yup.string().required(t('fieldRequired', { field: t('state') })),
  hsDistrictOfInstitution: yup.string().required(t('fieldRequired', { field: t('district') })),
  hsRegisterNumber: yup.string()
    .required(t('fieldRequired', { field: t('registerNumber') }))
    .matches(/^[a-zA-Z0-9]+$/, t('onlyLettersAndNumbers')),
  hsYearOfCompletion: yup.string().required(t('fieldRequired', { field: t('year') })),
  streamCriteria: yup.string().required(t('fieldRequired', { field: t('grade') })),
  hsMarksObtained: yup.string().required('Marks Obtained is a required field'),
  hsTotalMarks: yup.string().required('Total Marks is a required field'),
  courseMode: yup.string().oneOf(['REGULAR', 'DISTANCE'], 'Course Mode is a required').required('Course Mode is a required'),
  currentInstitutionName: yup.string().required('Institution Name is a required field'),
  currentInstitutionType: yup.string().oneOf(['Government', 'AIDED', 'PRIVATE'], 'InstitutionType is a required field').required('InstitutionType is a required field'),
  dateOfAdmission: yup
    .date()
    .transform((value, originalValue) => (originalValue === '' ? undefined : value))
    .required('Date of Admission is required'),
  academicYear: yup.string().required('Academic Year is a required field')
});

export const masterGraduationSchema = yup.object().shape({
  competitiveExam: yup.string().required('Competitive Exam All India Level is a required field'),
  institutionLocation: yup.string().required('Institution Location is a required field'),
  institutionName: yup.string().required('Institution Name is a required field'),
  registerNumber: yup.string().required('Registration Number is a required field'),
  streamCriteria: yup.string().required('Stream Criteria is a required field'),
  ugInstitutionType: yup.string().oneOf(['AIDED', 'GOVERNMENT', 'PRIVATE'], 'Institution Type is required').required('Institution Type is required'),
  underGradCourseName: yup.string().required('Course Name is a required field'),
  underGradDistrictOfInstitution: yup.string().required('District of Institution is a required field'),
  underGradStateOfInstitution: yup.string().required('State of Institution is a required field'),
  underGradStreamCriteria: yup.string().required('Grade is a required field'),
  underGradYearOfCompletion: yup.string().length(4).required(),
  university: yup.string().required('University is a required field'),
  courseMode: yup.string().oneOf(['REGULAR', 'DISTANCE'], 'Course Mode is a required').required('Course Mode is a required'),
  currentInstitutionName: yup.string().required('Institution Name is a required field'),
  currentInstitutionType: yup.string().oneOf(['Government', 'AIDED', 'PRIVATE'], 'InstitutionType is a required field').required('InstitutionType is a required field'),
  dateOfAdmission: yup
    .date()
    .transform((value, originalValue) => (originalValue === '' ? undefined : value))
    .required('Date of Admission is required'),
  academicYear: yup.string().required('Academic Year is a required field')
});
