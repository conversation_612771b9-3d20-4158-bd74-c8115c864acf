import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  GridItem,
  FormController,
  TitledCard,
  SectionHeading
} from 'common/components';
import { t } from 'i18next';
import {
  YES_NO_OPTIONS,
  YES_OR_NO
} from 'pages/common/constant';
import { STATE } from 'common/constants';
import {
  useFetchGenderQuery,
  useFetchStateQuery,
  useFetchDistrictQuery
} from 'pages/common/api';
import { FormLabel } from 'common/custom-components';
import { inputHandlers } from 'utils/inputHelpers';
import { _ } from 'utils/lodash';
import { useDispatch, useSelector } from 'react-redux';
import { actions as commonActions } from 'pages/common/slice';
import { selectorWithKey } from 'utils/common';
import { getOtp } from 'pages/common/selectors';

const ApplicantDetails = ({
  control, errors, watch, register, setValue, trigger, setError, clearErrors, getValues
}) => {
  const dispatch = useDispatch();

  // Fetch common data from APIs
  const { data: genderOptions = {}, isLoading: genderLoading } = useFetchGenderQuery();
  const { data: stateOptions = [], isLoading: stateLoading } = useFetchStateQuery(STATE.id);
  const {
    data: districtOptions = [], isLoading: districtLoading
  } = useFetchDistrictQuery(STATE.id);

  const isNriParent = watch('isNriParent');
  const isResident = watch('isResident');
  const isDifferentlyAbled = watch('isDifferentlyAbled');
  const watchedAdhaarNo = watch('aadhaarNumber');

  const { isVerified = false } = selectorWithKey(useSelector(getOtp), watchedAdhaarNo) || {};

  // Show toast when "No" is selected for Kerala origin
  useEffect(() => {
    if (isResident === YES_OR_NO.NO) {
      dispatch(commonActions.setCustomToast({
        open: true,
        variant: 'warning',
        title: t('eligibilityAlert'),
        message: t('keralaOriginRequired')
      }));
    }
  }, [isResident, dispatch]);

  return (
    <TitledCard title={t('personalDetails')}>
      <Box p={{ base: 0, md: 6 }}>
        <Grid templateColumns="repeat(12, 1fr)" gap={{ base: 6, md: 6, lg: 8 }}>
          {/* First Name */}
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('firstName')}
              name="firstName"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('firstName') })}
              required
              onInput={inputHandlers.name}
            />
          </GridItem>

          {/* Middle Name */}
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('middleName')}
              name="middleName"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('middleName') })}
              onInput={inputHandlers.name}
            />
          </GridItem>

          {/* Last Name */}
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('lastName')}
              name="lastName"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('lastName') })}
              onInput={inputHandlers.name}
            />
          </GridItem>

          {/* Date of Birth */}
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="date"
              label={t('dateOfBirth')}
              name="dateOfBirth"
              control={control}
              errors={errors}
              required
              maxDate={new Date(new Date().setFullYear(new Date().getFullYear() - 16))}
              minDate={new Date('2000-01-01')}
            />
          </GridItem>

          {/* Gender */}
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('gender')}
              name="gender"
              control={control}
              errors={errors}
              options={_.get(genderOptions, 'payload', [])}
              optionKey="id"
              required
              placeholder={t('fieldSelect', { field: t('gender') })}
              isLoading={genderLoading}
            />
          </GridItem>

          {/* Aadhaar Number */}
          <FormController
            name="aadhaarNumber"
            labelInput="aadhaarNumber"
            placeholder={t('fieldEnter', { field: t('aadhaarNumber') })}
            type="otpInput"
            otpType="aadhaar"
            control={control}
            errors={errors}
            register={register}
            setValue={setValue}
            trigger={trigger}
            verified={isVerified}
            setError={setError}
            clearErrors={clearErrors}
            getValues={getValues}
            required
          />
        </Grid>

        {/* Permanent Address Section */}
        <SectionHeading title={t('permanentAddress')} mt={8} mb={8} />

        <Grid templateColumns="repeat(12, 1fr)" gap={{ base: 6, md: 6, lg: 8 }}>
          {/* House Number/Address */}
          <GridItem colSpan={[12, 12, 6]}>
            <FormController
              type="text"
              label={t('houseNumber')}
              name="houseNumber"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('houseNumber') })}
              required
            />
          </GridItem>

          {/* Street/Locality */}
          <GridItem colSpan={[12, 12, 6]}>
            <FormController
              type="text"
              label={t('streetLocality')}
              name="streetLocality"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('streetLocality') })}
              required
            />
          </GridItem>

          {/* City/Town */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="text"
              label={t('cityTown')}
              name="cityTown"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('cityTown') })}
              required
            />
          </GridItem>

          {/* State */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="select"
              label={t('state')}
              name="state"
              control={control}
              errors={errors}
              options={stateOptions}
              optionKey="id"
              required
              placeholder={t('fieldSelect', { field: t('state') })}
              disabled
              isLoading={stateLoading}
            />
          </GridItem>

          {/* District */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="select"
              label={t('district')}
              name="district"
              control={control}
              errors={errors}
              options={_.get(districtOptions, 'payload', [])}
              optionKey="id"
              required
              placeholder={t('fieldSelect', { field: t('district') })}
              isLoading={districtLoading}
            />
          </GridItem>
          {/* Pincode */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="number"
              label={t('pincode')}
              name="pincode"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('pincode') })}
              required
              onInput={inputHandlers.pincode}
            />
          </GridItem>

          {/* Mobile Number */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="number"
              label={t('mobileNumber')}
              name="mobileNumber"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('mobileNumber') })}
              required
              onInput={inputHandlers.mobileWithValidation}
            />
          </GridItem>

          {/* Email Id */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="text"
              label={t('emailId')}
              name="emailId"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('emailId') })}
              required
            />
          </GridItem>
        </Grid>

        {/* Questions Section */}
        <Box mt={8}>
          <Grid templateColumns="repeat(12, 1fr)" gap={6}>
            {/* Are you a Resident? */}
            <GridItem colSpan={12}>
              <Grid templateColumns="repeat(12, 1fr)" gap={4}>
                <GridItem colSpan={12}>
                  <FormLabel label={t('isResident')} required />
                  <FormController
                    type="radio"
                    label={t('isResident')}
                    name="isResident"
                    control={control}
                    errors={errors}
                    options={YES_NO_OPTIONS}
                    optionKey="code"
                    required
                    direction="row"
                  />
                </GridItem>
              </Grid>
            </GridItem>

            {/* Is this applicant a ward of an NRI parent? */}
            <GridItem colSpan={12}>
              <Grid templateColumns="repeat(12, 1fr)" gap={4}>
                <GridItem colSpan={12}>
                  <FormLabel label={t('isNriParent')} required />
                  <FormController
                    type="radio"
                    label={t('isNriParent')}
                    name="isNriParent"
                    control={control}
                    errors={errors}
                    options={YES_NO_OPTIONS}
                    optionKey="code"
                    required
                    direction="row"
                    handleChange={() => {
                      setValue('pravasiIdCardNumber', '');
                    }}
                  />
                </GridItem>
              </Grid>
            </GridItem>

            {/* Conditional NRI field */}
            {isNriParent === YES_OR_NO.YES && (
              <GridItem colSpan={12}>
                <Grid templateColumns="repeat(12, 1fr)" gap={4}>
                  <GridItem colSpan={[12, 6, 4]}>
                    <FormController
                      type="text"
                      label={t('pravasiIdCardNumber')}
                      name="pravasiIdCardNumber"
                      control={control}
                      errors={errors}
                      placeholder={t('fieldEnter', { field: t('pravasiIdCardNumber') })}
                      required
                    />
                  </GridItem>
                </Grid>
              </GridItem>
            )}

            {/* Are you a Differently Abled Candidate? */}
            <GridItem colSpan={12}>
              <Grid templateColumns="repeat(12, 1fr)" gap={4}>
                <GridItem colSpan={12}>
                  <FormLabel label={t('isDifferentlyAbled')} required />
                  <FormController
                    type="radio"
                    label={t('isDifferentlyAbled')}
                    name="isDifferentlyAbled"
                    control={control}
                    errors={errors}
                    options={YES_NO_OPTIONS}
                    optionKey="code"
                    required
                    direction="row"
                    handleChange={() => {
                      setValue('percentageOfDisability', null);
                    }}
                  />
                </GridItem>
              </Grid>
            </GridItem>
            {/* Conditional Percentage of Disability field */}
            {isDifferentlyAbled === YES_OR_NO.YES && (
              <GridItem colSpan={12}>
                <Grid templateColumns="repeat(12, 1fr)" gap={4}>
                  <GridItem colSpan={[12, 6, 4]} mb={{ base: 4, md: 0 }}>
                    <FormController
                      type="number"
                      label={t('percentageOfDisability')}
                      name="percentageOfDisability"
                      control={control}
                      errors={errors}
                      placeholder={t('fieldEnter', { field: t('percentageOfDisability') })}
                      required
                      min={60}
                      max={100}
                      onInput={inputHandlers.percentageWithRange}
                    />
                  </GridItem>
                </Grid>
              </GridItem>
            )}
          </Grid>
        </Box>
      </Box>
    </TitledCard>
  );
};

export default ApplicantDetails;
