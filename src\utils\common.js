import { APPLICATION_STATUS } from 'pages/common/constant';
import { createSelector } from 'reselect';

export const selector<PERSON><PERSON><PERSON><PERSON> = createSelector(
  [
    (state) => state,
    (_state, key) => key
  ],
  (items, category) => {
    return items[category];
  }
);

export const getScholarshipTypeDisplayName = (code) => {
  const mapping = {
    HSS: 'Higher Secondary (HSS)',
    UG: 'Under Graduation (UG)',
    PG: 'Post Graduation (PG)'
  };
  return mapping[code] || code;
};

export const getStatusColor = (data) => {
  switch (data) {
    case APPLICATION_STATUS.DRAFT:
      return '#DDDDDD';
    case APPLICATION_STATUS.APPROVED:
      return '#DCFCE7';
    case APPLICATION_STATUS.APPLIED:
      return '#DBEAFE';
    case APPLICATION_STATUS.VERIFIED:
      return '#FEF9C3';
    case APPLICATION_STATUS.REJECTED:
      return 'rgba(252, 85, 85, 1)';
    default:
      return 'rgba(0, 178, 235, 1)';
  }
};
