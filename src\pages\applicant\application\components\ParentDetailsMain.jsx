import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Stack } from 'common/components';
import { StepperButtons } from 'common/custom-components';
import { t } from 'i18next';
import { actions as commonActions } from 'pages/common/slice';
import { getFileInfo, getOtp } from 'pages/common/selectors';
import { useCustomToast, useFocusToField, useScrollToError } from 'utils/hooks';
import { APPLICATION_STATUS } from 'pages/common/constant';
import {
  parentDetailsSchema, PARENT_DETAILS_DEFAULT_VALUES
} from '../validation/parentDetailsSchema';
import ParentDetails from './ParentDetails';
import { FinancialDetails } from './FinancialDetails';
import { useSaveParentDetailsMutation, useUpdateParentDetailsMutation } from '../api';
import { getCareStatusFromCircumstances, isNameMatchingAadhaarFromOtp, transformParentDetailsToPayload } from '../helpers';
import { getParentGuardianId } from '../selectors';
import { CARE_STATUS, CURRENT_CARE_PROVIDER } from '../constants';

const ParentDetailsMain = ({
  onNext,
  onPrevious,
  applicationDetails,
  isDetailsSuccess,
  applicationId
}) => {
  const dispatch = useDispatch();
  const scrollToError = useScrollToError();
  const focusToField = useFocusToField();
  const customToast = useCustomToast();

  const [saveParentDetails, {
    isLoading: isSaveLoading, isSuccess: isSaveSuccess
  }] = useSaveParentDetailsMutation();
  const [updateParentDetails, {
    isLoading: isUpdateLoading, isSuccess: isUpdateSuccess
  }] = useUpdateParentDetailsMutation();

  const parentGuardianId = useSelector(getParentGuardianId);
  const otpState = useSelector(getOtp);
  const { applicationStatus = '' } = useSelector(getFileInfo);

  const isLoading = isSaveLoading || isUpdateLoading;
  const isSuccess = isSaveSuccess || isUpdateSuccess;

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    watch,
    reset,
    setValue,
    register,
    trigger,
    setError,
    clearErrors,
    getValues
  } = useForm({
    defaultValues: PARENT_DETAILS_DEFAULT_VALUES,
    resolver: yupResolver(parentDetailsSchema),
    mode: 'onChange'
  });

  // Reset form with API data when available
  useEffect(() => {
    if (!isDetailsSuccess || !applicationDetails?.parentGuardianDetails) return;

    const parentData = applicationDetails.parentGuardianDetails;

    const getCurrentCareProvider = () => {
      if (parentData.guardian) return CURRENT_CARE_PROVIDER.GUARDIAN;
      if (parentData.institution) return CURRENT_CARE_PROVIDER.INSTITUTION;
      return '';
    };

    const predeterminedCareStatus = getCareStatusFromCircumstances(
      applicationDetails?.personalDetails
    );

    const formValues = {
      applicantCareStatus: predeterminedCareStatus || parentData.parentOrGuardian || '',
      currentCareProvider: getCurrentCareProvider(),
      annualFamilyIncome: parentData.annualIncome || '',
      incomeCertificateNo: parentData.incomeCertificateNumber || '',
      incomeCertificateIssuedBy: parentData.certificateIssuedBy || '',
      certificateIssuedDate: parentData.certificateIssuedDate
        ? new Date(parentData.certificateIssuedDate) : null
    };

    switch (parentData.parentOrGuardian) {
      case CARE_STATUS.PARENTS:
        Object.assign(formValues, {
          fatherName: parentData.fatherName || '',
          fatherContactNumber: parentData.fatherContactNumber || '',
          fatherAadhaarNumber: parentData.fatherAadhaarNumber || '',
          motherName: parentData.motherName || '',
          motherContactNumber: parentData.motherContactNumber || '',
          motherAadhaarNumber: parentData.motherAadhaarNumber || ''
        });
        break;

      case CARE_STATUS.SINGLE_PARENT: {
        const isFather = !!parentData.fatherName;
        Object.assign(formValues, {
          parentName: isFather ? parentData.fatherName : parentData.motherName,
          contactNumber: isFather ? parentData.fatherContactNumber : parentData.motherContactNumber,
          aadhaarNumber: isFather ? parentData.fatherAadhaarNumber : parentData.motherAadhaarNumber,
          relationshipToApplicant: isFather ? 'FATHER' : 'MOTHER'
        });
        break;
      }

      case CARE_STATUS.ORPHAN:
        if (formValues.currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN) {
          Object.assign(formValues, {
            guardianName: parentData.guardianName || '',
            guardianRelationshipToApplicant: parentData.guardianRelation || '',
            guardianContactNumber: parentData.guardianContactNumber || '',
            guardianAadhaarNumber: parentData.guardianAadhaarNumber || ''
          });
        } else if (formValues.currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION) {
          Object.assign(formValues, {
            institutionName: parentData.institutionName || '',
            institutionRegistrationNumber: parentData.institutionRegNumber || '',
            institutionContactNumber: parentData.institutionContactNumber || ''
          });
        }
        break;

      default:
        break;
    }

    reset({
      ...PARENT_DETAILS_DEFAULT_VALUES,
      ...formValues
    });

    if (parentData.fatherAadhaarNumber) {
      dispatch(commonActions.setOtpState({
        key: parentData.fatherAadhaarNumber,
        data: {
          isVerified: true,
          otpSuccess: false,
          verificationData: {
            uidVault: parentData.fatherAadhaarVaultNo,
            name: parentData.fatherNameAsAadhaar
          }
        }
      }));
    }

    if (parentData.motherAadhaarNumber) {
      dispatch(commonActions.setOtpState({
        key: parentData.motherAadhaarNumber,
        data: {
          isVerified: true,
          otpSuccess: false,
          verificationData: {
            uidVault: parentData.motherAadhaarVaultNo,
            name: parentData.motherNameAsAadhaar
          }
        }
      }));
    }

    if (parentData.guardianAadhaarNumber) {
      dispatch(commonActions.setOtpState({
        key: parentData.guardianAadhaarNumber,
        data: {
          isVerified: true,
          otpSuccess: false,
          verificationData: {
            uidVault: parentData.guardianAadhaarVaultNo,
            name: parentData.guardianNameAsAadhaar
          }
        }
      }));
    }
  }, [isDetailsSuccess, applicationDetails, dispatch]);

  const shouldSkipSave = !isDirty || (
    applicationStatus && applicationStatus !== APPLICATION_STATUS.DRAFT
  );

  const onSubmit = (data) => {
    if (!applicationId) {
      return;
    }

    if (shouldSkipSave) {
      onNext();
      return;
    }

    const payload = transformParentDetailsToPayload(data, applicationId, otpState);

    let hasErrors = false;

    switch (data.applicantCareStatus) {
      case CARE_STATUS.PARENTS:
        if (!payload.fatherAadhaarVaultNo || !payload.motherAadhaarVaultNo) {
          customToast('noAadhaarVaultId');
          focusToField('fatherAadhaarNumber');
          hasErrors = true;
        }
        if (!isNameMatchingAadhaarFromOtp(data.fatherName, data.fatherAadhaarNumber, otpState)) {
          customToast('aadhaarNameMismatch');
          setValue('fatherName', '');
          focusToField('fatherName');
          hasErrors = true;
        }
        if (!isNameMatchingAadhaarFromOtp(data.motherName, data.motherAadhaarNumber, otpState)) {
          customToast('aadhaarNameMismatch');
          setValue('motherName', '');
          focusToField('motherName');
          hasErrors = true;
        }
        break;

      case CARE_STATUS.ORPHAN:
        if (data.currentCareProvider !== CURRENT_CARE_PROVIDER.INSTITUTION
          && !payload.guardianAadhaarVaultNo) {
          customToast('noAadhaarVaultId');
          focusToField('guardianAadhaarNumber');
          hasErrors = true;
        }
        if (data.currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN
          && !isNameMatchingAadhaarFromOtp(
            data.guardianName,
            data.guardianAadhaarNumber,
            otpState
          )) {
          customToast('aadhaarNameMismatch');
          setValue('guardianName', '');
          focusToField('guardianName');
          hasErrors = true;
        }
        break;

      case CARE_STATUS.SINGLE_PARENT:
        if ((data.relationshipToApplicant === 'FATHER' && !payload.fatherAadhaarVaultNo)
          || (data.relationshipToApplicant === 'MOTHER' && !payload.motherAadhaarVaultNo)) {
          customToast('noAadhaarVaultId');
          focusToField('aadhaarNumber');
          hasErrors = true;
        } else if (data.relationshipToApplicant === 'FATHER'
          && !isNameMatchingAadhaarFromOtp(data.parentName, data.aadhaarNumber, otpState)) {
          customToast('aadhaarNameMismatch');
          setValue('parentName', '');
          focusToField('parentName');
          hasErrors = true;
        } else if (data.relationshipToApplicant === 'MOTHER'
          && !isNameMatchingAadhaarFromOtp(data.parentName, data.aadhaarNumber, otpState)) {
          customToast('aadhaarNameMismatch');
          setValue('parentName', '');
          focusToField('parentName');
          hasErrors = true;
        }
        break;

      default:
        break;
    }

    if (!hasErrors) {
      if (parentGuardianId) {
        updateParentDetails({ id: parentGuardianId, ...payload }).unwrap();
      } else {
        saveParentDetails(payload).unwrap();
      }
    }
  };

  useEffect(() => {
    if (isSuccess) {
      onNext();
    }
  }, [isSuccess, onNext]);

  return (
    <Box as="form" onSubmit={handleSubmit(onSubmit, scrollToError)}>
      <Stack spacing={6}>
        {/* Parent Details Section */}
        <ParentDetails
          control={control}
          errors={errors}
          watch={watch}
          applicationDetails={applicationDetails}
          setValue={setValue}
          register={register}
          trigger={trigger}
          setError={setError}
          clearErrors={clearErrors}
          getValues={getValues}
        />

        {/* Financial Details Section */}
        <FinancialDetails control={control} errors={errors} watch={watch} />

        {/* Action Buttons */}
        <StepperButtons
          currentStep={1}
          totalSteps={6}
          onPrevious={onPrevious}
          nextButtonProps={{
            isLoading,
            loadingText: t('saving')
          }}
          layout="space-between"
        />
      </Stack>
    </Box>
  );
};

export default ParentDetailsMain;
