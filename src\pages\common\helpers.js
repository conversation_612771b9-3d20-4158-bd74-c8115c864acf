import { t } from 'i18next';
import { _ } from 'utils/lodash';
import { translateKey } from 'utils/translate';
import { CARE_STATUS, CURRENT_CARE_PROVIDER, DOCUMENT_META_DATA } from '../applicant/application/constants';
import {
  formatDisplayDate,
  formatBooleanToText,
  getDisplayValue,
  getNestedValue,
  formatFamilyCircumstances
} from '../../utils/reviewUtils';

// === Data Generation Functions ===

/**
 * Generate personal details data for review display
 * @param {object} applicationDetails - Application details object
 * @param {boolean} isDetailsSuccess - Success flag for data loading
 * @returns {Array} - Array of formatted data objects
 */
const getPersonalDetailsData = (applicationDetails, isDetailsSuccess) => {
  if (!isDetailsSuccess || !applicationDetails) return [];

  return [
    { label: t('firstName'), value: getDisplayValue(applicationDetails.firstName), colSpan: [12, 6, 4] },
    { label: t('middleName'), value: getDisplayValue(applicationDetails.middleName), colSpan: [12, 6, 4] },
    { label: t('lastName'), value: getDisplayValue(applicationDetails.lastName), colSpan: [12, 6, 4] },
    { label: t('dateOfBirth'), value: formatDisplayDate(applicationDetails.dateOfBirth), colSpan: [12, 6, 4] },
    { label: t('gender'), value: getNestedValue(applicationDetails, 'gender.name'), colSpan: [12, 6, 4] },
    { label: t('mobileNumber'), value: getDisplayValue(applicationDetails.contactNumber), colSpan: [12, 6, 4] },
    { label: t('emailId'), value: getDisplayValue(applicationDetails.emailId), colSpan: [12, 6, 4] },
    { label: t('aadhaarNumber'), value: getDisplayValue(applicationDetails.aadhaarNumber), colSpan: [12, 6, 4] },
    { label: t('country'), value: getDisplayValue(applicationDetails.country, 'India'), colSpan: [12, 6, 4] },
    { label: t('state'), value: getNestedValue(applicationDetails, 'state.name'), colSpan: [12, 6, 4] },
    { label: t('district'), value: getNestedValue(applicationDetails, 'district.name'), colSpan: [12, 6, 4] },
    { label: t('pincode'), value: getDisplayValue(applicationDetails.pincode), colSpan: [12, 6, 4] },
    { label: t('houseNumber'), value: getDisplayValue(applicationDetails.houseNoName), colSpan: [12, 6, 4] },
    { label: t('streetLocality'), value: getDisplayValue(applicationDetails.streetLocality), colSpan: [12, 6, 4] },
    { label: t('cityTown'), value: getDisplayValue(applicationDetails.cityTown), colSpan: [12, 6, 4] },
    { label: t('isResident'), value: formatBooleanToText(applicationDetails.keralite), colSpan: [12, 6, 4] },
    { label: t('isNriParent'), value: formatBooleanToText(applicationDetails.nriParent), colSpan: [12, 6, 4] },
    ...(applicationDetails.nriParent ? [
      { label: t('pravasiIdCardNumber'), value: getDisplayValue(applicationDetails.pravasiIdCardNumber), colSpan: [12, 6, 4] }
    ] : []),
    { label: t('isDifferentlyAbled'), value: formatBooleanToText(applicationDetails.studentDisable), colSpan: [12, 6, 4] },
    ...(applicationDetails.studentDisable ? [
      { label: t('percentageOfDisability'), value: getDisplayValue(applicationDetails.disabilityPercentage), colSpan: [12, 6, 4] }
    ] : []),
    { label: t('additionalDetails'), colSpan: [12, 12, 12], type: 'subheading' },
    { label: t('familyCircumstances'), value: formatFamilyCircumstances(applicationDetails), colSpan: [12, 6, 4] },
    { label: t('hasRepresentedAtStateLevel'), value: formatBooleanToText(applicationDetails.sportsArtsAchievement), colSpan: [12, 6, 4] }
  ];
};

/**
 * Generate parent/guardian details data for review display
 * @param {object} applicationDetails - Application details object
 * @param {boolean} isDetailsSuccess - Success flag for data loading
 * @returns {Array} - Array of formatted data objects
 */
const getParentGuardianDetailsData = (applicationDetails, isDetailsSuccess) => {
  if (!isDetailsSuccess || !applicationDetails?.parentGuardianDetails) return [];

  const { parentGuardianDetails } = applicationDetails;
  const data = [];

  const careStatus = parentGuardianDetails.parentOrGuardian;

  // Determine current care provider for orphans
  let currentCareProvider = '';
  if (careStatus === CARE_STATUS.ORPHAN) {
    if (parentGuardianDetails.guardian) {
      currentCareProvider = CURRENT_CARE_PROVIDER.GUARDIAN;
    } else if (parentGuardianDetails.institution) {
      currentCareProvider = CURRENT_CARE_PROVIDER.INSTITUTION;
    }
  }

  // Helper function to get care status display text
  const getCareStatusDisplayText = (status) => {
    switch (status) {
      case CARE_STATUS.PARENTS:
        return 'Under The Care Of Parents';
      case CARE_STATUS.SINGLE_PARENT:
        return 'Under The Care Of A Single Parent';
      case CARE_STATUS.ORPHAN:
        return 'Orphan (No Living Parents)';
      default:
        return status;
    }
  };

  // Helper function to get care provider display text
  const getCareProviderDisplayText = (provider) => {
    switch (provider) {
      case CURRENT_CARE_PROVIDER.GUARDIAN:
        return 'Guardian';
      case CURRENT_CARE_PROVIDER.INSTITUTION:
        return 'Institution';
      default:
        return provider;
    }
  };

  // Section: Parent / Guardian Status
  data.push({
    label: t('applicantCareStatus'),
    value: getCareStatusDisplayText(careStatus),
    colSpan: [12, 6, 4]
  });

  if (careStatus === CARE_STATUS.PARENTS) {
    data.push(
      { label: t('fatherDetails'), type: 'subheading', colSpan: [12, 12, 12] },
      { label: t('name'), value: getDisplayValue(parentGuardianDetails.fatherName), colSpan: [12, 6, 4] },
      { label: t('contactNumber'), value: getDisplayValue(parentGuardianDetails.fatherContactNumber), colSpan: [12, 6, 4] },
      { label: t('aadhaarNumber'), value: getDisplayValue(parentGuardianDetails.fatherAadhaarNumber), colSpan: [12, 6, 4] },
      { label: t('motherDetails'), type: 'subheading', colSpan: [12, 12, 12] },
      { label: t('name'), value: getDisplayValue(parentGuardianDetails.motherName), colSpan: [12, 6, 4] },
      { label: t('contactNumber'), value: getDisplayValue(parentGuardianDetails.motherContactNumber), colSpan: [12, 6, 4] },
      { label: t('aadhaarNumber'), value: getDisplayValue(parentGuardianDetails.motherAadhaarNumber), colSpan: [12, 6, 4] }
    );
  }

  if (careStatus === CARE_STATUS.SINGLE_PARENT) {
    const isFather = !!parentGuardianDetails.fatherName;

    data.push(
      { label: t('parentDetails'), type: 'subheading', colSpan: [12, 12, 12] },
      {
        label: t('parentName'),
        value: getDisplayValue(isFather
          ? parentGuardianDetails.fatherName
          : parentGuardianDetails.motherName),
        colSpan: [12, 6, 4]
      },
      {
        label: t('relationshipToApplicant'),
        value: getDisplayValue(isFather ? 'Father' : 'Mother'),
        colSpan: [12, 6, 4]
      },
      {
        label: t('contactNumber'),
        value: getDisplayValue(isFather
          ? parentGuardianDetails.fatherContactNumber
          : parentGuardianDetails.motherContactNumber),
        colSpan: [12, 6, 4]
      },
      {
        label: t('aadhaarNumber'),
        value: getDisplayValue(isFather
          ? parentGuardianDetails.fatherAadhaarNumber
          : parentGuardianDetails.motherAadhaarNumber),
        colSpan: [12, 6, 4]
      }
    );
  }

  if (careStatus === CARE_STATUS.ORPHAN) {
    data.push({ label: t('orphanCareProvider'), value: getCareProviderDisplayText(currentCareProvider), colSpan: [12, 6, 4] });

    if (currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN) {
      data.push(
        { label: t('guardianDetails'), type: 'subheading', colSpan: [12, 12, 12] },
        { label: t('guardianName'), value: getDisplayValue(parentGuardianDetails.guardianName), colSpan: [12, 6, 4] },
        { label: t('guardianRelation'), value: getDisplayValue(parentGuardianDetails.guardianRelation), colSpan: [12, 6, 4] },
        { label: t('guardianContactNumber'), value: getDisplayValue(parentGuardianDetails.guardianContactNumber), colSpan: [12, 6, 4] },
        { label: t('guardianAadhaarNumber'), value: getDisplayValue(parentGuardianDetails.guardianAadhaarNumber), colSpan: [12, 6, 4] }
      );
    } else if (currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION) {
      data.push(
        { label: t('institutionDetails'), type: 'subheading', colSpan: [12, 12, 12] },
        { label: t('institutionName'), value: getDisplayValue(parentGuardianDetails.institutionName), colSpan: [12, 6, 4] },
        { label: t('institutionRegNumber'), value: getDisplayValue(parentGuardianDetails.institutionRegNumber), colSpan: [12, 6, 4] },
        { label: t('institutionContactNumber'), value: getDisplayValue(parentGuardianDetails.institutionContactNumber), colSpan: [12, 6, 4] },
        { label: t('institutionAddress'), value: getDisplayValue(parentGuardianDetails.institutionAddress), colSpan: [12, 6, 4] }
      );
    }
  }

  // Financial Info
  data.push(
    { label: t('financialInformation'), type: 'subheading', colSpan: [12, 12, 12] },
    { label: t('annualFamilyIncome'), value: getDisplayValue(parentGuardianDetails.annualIncome), colSpan: [12, 6, 4] },
    { label: t('incomeCertificateIssuedBy'), value: getDisplayValue(parentGuardianDetails.certificateIssuedBy), colSpan: [12, 6, 4] },
    { label: t('incomeCertificateNo'), value: getDisplayValue(parentGuardianDetails.incomeCertificateNumber), colSpan: [12, 6, 4] },
    { label: t('certificateIssuedDate'), value: formatDisplayDate(parentGuardianDetails.certificateIssuedDate), colSpan: [12, 6, 4] }
  );

  return data;
};

/**
 * Generate bank details data for review display
 * @param {object} applicationDetails - Application details object
 * @param {boolean} isDetailsSuccess - Success flag for data loading
 * @returns {Array} - Array of formatted data objects
 */
const getBankDetailsData = (applicationDetails, isDetailsSuccess) => {
  if (!isDetailsSuccess || !applicationDetails?.bankDetails) return [];

  const { bankDetails } = applicationDetails;
  return [
    { label: t('accountHolderName'), value: getDisplayValue(bankDetails.accountHolderName), colSpan: [12, 6, 4] },
    { label: t('accountNumber'), value: getDisplayValue(bankDetails.accountNumber), colSpan: [12, 6, 4] },
    { label: t('district'), value: getNestedValue(bankDetails, 'district.name'), colSpan: [12, 6, 4] },
    { label: t('bankName'), value: getNestedValue(bankDetails, 'bank.bankName'), colSpan: [12, 6, 4] },
    { label: t('branchName'), value: getNestedValue(bankDetails, 'bankBranch.branchName'), colSpan: [12, 6, 4] },
    { label: t('ifscCode'), value: getDisplayValue(bankDetails.ifscCode), colSpan: [12, 6, 4] }
  ];
};

/**
 * Generate current course details data for review display
 * @param {object} applicationDetails - Application details object
 * @param {boolean} isDetailsSuccess - Success flag for data loading
 * @returns {Array} - Array of formatted data objects
 */
const getCurrentCourseDetailsData = (applicationDetails, isDetailsSuccess) => {
  if (!isDetailsSuccess || !applicationDetails?.academicDetails) return [];

  const { academicDetails } = applicationDetails;
  return [
    { label: t('currentCourseDetails'), colSpan: [12, 12, 12], type: 'subheading' },
    { label: t('institutionType'), value: getDisplayValue(academicDetails.currentInstitutionType), colSpan: [12, 6, 4] },
    { label: t('institutionName'), value: getDisplayValue(academicDetails.currentInstitutionName), colSpan: [12, 6, 4] },
    { label: t('courseMode'), value: getDisplayValue(academicDetails.courseMode), colSpan: [12, 6, 4] },
    { label: t('academicYear'), value: getDisplayValue(academicDetails.academicYear), colSpan: [12, 6, 4] },
    { label: t('dateOfAdmission'), value: formatDisplayDate(academicDetails.dateOfAdmission), colSpan: [12, 6, 4] }
  ];
};

/**
 * Generate previous academic details data for review display
 * @param {object} applicationDetails - Application details object
 * @param {boolean} isDetailsSuccess - Success flag for data loading
 * @returns {Array} - Array of formatted data objects
 */
const getPreviousAcademicDetailsData = (applicationDetails, isDetailsSuccess) => {
  if (!isDetailsSuccess || !applicationDetails?.academicDetails) return [];

  const { academicDetails } = applicationDetails;
  const { streamCriteria } = academicDetails;
  const data = [];
  if (
    streamCriteria.educationQualification.scholarshipType === 'HSS'
  || streamCriteria.educationQualification.scholarshipType === 'UG'
  ) {
    data.push(
      ...(streamCriteria.educationQualification.scholarshipType === 'HSS'
        ? [{ label: t('sslcDetails'), colSpan: [12, 12, 12], type: 'subheading' }]
        : []),
      ...(streamCriteria.educationQualification.scholarshipType === 'UG'
        ? [{ label: t('higherSecondaryDetails'), colSpan: [12, 12, 12], type: 'subheading' }]
        : []),

      { label: t('board'), value: getNestedValue(academicDetails, 'board.name'), colSpan: [12, 6, 4] },
      { label: t('institutionType'), value: getDisplayValue(academicDetails.institutionType), colSpan: [12, 6, 4] },
      { label: t('institutionName'), value: getDisplayValue(academicDetails.institutionName), colSpan: [12, 6, 4] },
      { label: t('institutionLocation'), value: getDisplayValue(academicDetails.institutionLocation), colSpan: [12, 6, 4] },
      { label: t('stateOfInstitution'), value: getNestedValue(academicDetails, 'institutionState.name'), colSpan: [12, 6, 4] },
      { label: t('districtOfInstitution'), value: getNestedValue(academicDetails, 'institutionDistrict.name'), colSpan: [12, 6, 4] },
      { label: t('registerNumber'), value: getDisplayValue(academicDetails.registerNumber), colSpan: [12, 6, 4] },
      { label: t('yearOfCompletion'), value: getDisplayValue(academicDetails.yearOfPassing), colSpan: [12, 6, 4] },
      ...(streamCriteria.educationQualification.scholarshipType === 'HSS'
        ? [{ label: t('grade'), value: getNestedValue(streamCriteria, 'name'), colSpan: [12, 6, 4] }]
        : []),
      { label: t('marksDetails'), colSpan: [12, 12, 12], type: 'subheading' },
      { label: t('marksObtained'), value: getDisplayValue(academicDetails.marksObtained), colSpan: [12, 6, 4] },
      { label: t('totalMarks'), value: getDisplayValue(academicDetails.totalMarks), colSpan: [12, 6, 4] },
      { label: t('percentage'), value: getDisplayValue(academicDetails.percentage), colSpan: [12, 6, 4] }
    );
  }

  if (streamCriteria.educationQualification.scholarshipType === 'PG') {
    data.push(
      { label: t('postgraduateDetails'), colSpan: [12, 12, 12], type: 'subheading' },
      { label: t('stateOfInstitution'), value: getNestedValue(academicDetails, 'institutionState.name'), colSpan: [12, 6, 4] },
      { label: t('university'), value: getNestedValue(academicDetails, 'university.name'), colSpan: [12, 6, 4] },
      { label: t('institutionType'), value: getDisplayValue(academicDetails.institutionType), colSpan: [12, 6, 4] },
      { label: t('institutionName'), value: getDisplayValue(academicDetails.institutionName), colSpan: [12, 6, 4] },
      { label: t('institutionLocation'), value: getDisplayValue(academicDetails.institutionLocation), colSpan: [12, 6, 4] },
      { label: t('districtOfInstitution'), value: getNestedValue(academicDetails, 'institutionDistrict.name'), colSpan: [12, 6, 4] },
      { label: t('courseName'), value: getDisplayValue(academicDetails.courseName), colSpan: [12, 6, 4] },
      { label: t('stream'), value: getNestedValue(academicDetails, 'stream.name'), colSpan: [12, 6, 4] },
      { label: t('yearOfCompletion'), value: getDisplayValue(academicDetails.yearOfPassing), colSpan: [12, 6, 4] },
      { label: t('registerNumber'), value: getDisplayValue(academicDetails.registerNumber), colSpan: [12, 6, 4] },
      { label: t('competitiveExam'), value: getDisplayValue(academicDetails.competitiveExam), colSpan: [12, 12, 12] },
      { label: t('gradingSystem'), colSpan: [12, 12, 12], type: 'subheading' },
      { label: t('gradingSystem'), value: getDisplayValue(academicDetails.gradeSystem), colSpan: [12, 6, 4] }
    );
  }

  // Grading system details
  if (academicDetails.gradeSystem === 'MARKS') {
    data.push(
      { label: t('marksObtained'), value: getDisplayValue(academicDetails.marksObtained), colSpan: [12, 6, 4] },
      { label: t('totalMarks'), value: getDisplayValue(academicDetails.totalMarks), colSpan: [12, 6, 4] },
      { label: t('percentage'), value: getDisplayValue(academicDetails.percentage), colSpan: [12, 6, 4] }
    );
  } else if (academicDetails.gradeSystem === 'CGPA') {
    data.push(
      { label: t('cgpaDetails'), colSpan: [12, 12, 12], type: 'subheading' },
      { label: t('score'), value: getDisplayValue(academicDetails.cgpaScore), colSpan: [12, 6, 4] },
      { label: t('percentage'), value: getDisplayValue(academicDetails.cgpaPercentage), colSpan: [12, 6, 4] }
    );
  }
  if (
    academicDetails.ccpaScore != null
  || academicDetails.ccpaPercentage != null
  ) {
    data.push(
      { label: t('ccpaDetails'), colSpan: [12, 12, 12], type: 'subheading' },
      { label: t('score'), value: getDisplayValue(academicDetails.ccpaScore), colSpan: [12, 6, 4] },
      { label: t('percentage'), value: getDisplayValue(academicDetails.ccpaPercentage), colSpan: [12, 6, 4] }
    );
  }
  return data;
};

const getDocumentDetails = (data) => {
  if (_.isEmpty(data)) return [];
  const returnData = Object.entries(DOCUMENT_META_DATA).map(([key, { name = '' }]) => {
    if (name && !_.isEmpty(data[`${name}Url`]) && data[`${name}Url`] !== null) {
      const {
        ext, id, type, url
      } = _.get(data, `${name}Url`, {});
      return {
        ext, id, label: translateKey(name), name: `${key}.${ext}`, type, url, status: 'uploaded'
      };
    }
    return null;
  });
  return returnData.filter((item) => item !== null);
};

export {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData,
  getDocumentDetails
};
