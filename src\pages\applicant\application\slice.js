import { createSlice } from '@reduxjs/toolkit';
import { _ } from 'utils/lodash';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  currentStep: 0,
  formData: {},
  applicationId: null,
  formIds: {
    parentGuardianId: null,
    applicationStatus: '',
    bankDetailsId: null,
    academicDetailsId: null,
    documentsId: null
  }
};

const applicationSlice = createSlice({
  name: STATE_REDUCER_KEY.SLICE,
  initialState,
  reducers: {
    clearAll: () => initialState,
    setCurrentStep: (state, { payload }) => {
      _.set(state, 'currentStep', payload);
    },
    nextStep: (state) => {
      _.set(state, 'currentStep', state.currentStep + 1);
    },
    previousStep: (state) => {
      _.set(state, 'currentStep', state.currentStep - 1);
    },
    updateFormData: (state, { payload }) => {
      const { key, data } = payload;
      _.set(state, `formData.${key}`, { ..._.get(state, `formData.${key}`, {}), ...data });
    },
    setApplicationId: (state, { payload }) => {
      _.set(state, 'applicationId', payload);
    },
    setFormId: (state, { payload }) => {
      const { formType, id } = payload;
      _.set(state, `formIds.${formType}`, id);
    },
    clearFormIds: (state) => {
      _.set(state, 'formIds', initialState.formIds);
    }
  }
});

export const { actions, reducer } = applicationSlice;
