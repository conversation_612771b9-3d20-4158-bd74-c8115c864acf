import { fetchBaseQuery } from '@reduxjs/toolkit/query';
import { API_URL } from 'common';
import { STATE } from 'common/constants';
import { t } from 'i18next';
import { actions as commonActions } from 'pages/common/slice';
import { RESPONSE_TYPE } from 'pages/others/fileDownload/constant';
import { _ } from 'utils/lodash';
import { logout } from 'utils/auth';
import { WHITE_LIST_URL } from 'utils/constants';

/**
 *  API request handler for RTK Query - handles all common patterns
 * @param {Object} options - Configuration options
 * @param {string} options.successMessage - Success message to display
 * @param {Function} options.onSuccess - Custom success callback function
 * @param {Function} options.onError - Custom error callback function
 * @returns {Function} onQueryStarted function for RTK Query
 */
export const handleAPIRequest = (options = {}) => {
  const {
    showSuccessToast = true,
    successMessage,
    onSuccess,
    onError
  } = options;

  return async (arg, { dispatch, queryFulfilled }) => {
    try {
      const result = await queryFulfilled;
      // Use dynamic message from API response or fallback to provided successMessage
      const responseMessage = _.get(result, 'data.message', successMessage);

      if (showSuccessToast && responseMessage) {
        dispatch(
          commonActions.setCustomToast({
            open: true,
            variant: 'success',
            message: responseMessage,
            title: 'Success'
          })
        );
      }
      if (onSuccess) {
        onSuccess(result, arg, dispatch);
      }
      return result;
    } catch (error) {
      // Only handle custom error callbacks - error messages are handled by rtkQueryErrorHandler
      if (onError) {
        onError(error, arg, dispatch);
      }
      throw error;
    }
  };
};

export const rtkQueryErrorHandler = (api) => (next) => (action) => {
  if (_.has(action, 'error')) {
    const { status, data = {} } = action.payload;
    let message = _.get(data, 'errors.message', _.values(data.errors).map((msg) => `🔸${msg}`).join('\n'));
    let title = t('somethingUnexpected');

    const statusMessages = {
      401: t('unAuthorized'),
      403: t('permissionDenied'),
      404: t('notFound'),
      500: t('serverError'),
      502: t('serviceUnavailable'),
      503: t('serviceUnavailable'),
      504: t('serviceUnavailable')
    };

    if (status === 401) {
      const endpoint = _.get(action, 'meta.baseQueryMeta.request.url', '');
      const isWhitelisted = WHITE_LIST_URL.some((allowedUrl) => endpoint.includes(allowedUrl));

      if (!isWhitelisted) {
        logout();
        return next(action);
      }
    }

    if (_.has(data, 'errors')) {
      message = message || statusMessages[status] || t('pleaseTryAgain');
    } else if (_.has(data, 'error')) {
      message = _.get(data, 'message', statusMessages[status]);
      title = _.get(data, 'error', statusMessages[status]);
    } else {
      message = t('pleaseTryAgain');
    }

    api.dispatch(commonActions.setCustomToast({
      open: true,
      variant: [400, 405].includes(status) ? 'warning' : 'error',
      message,
      title
    }));
  }
  return next(action);
};

export const getBaseQuery = () => {
  const apiConfigs = {
    headers: {
      'X-STATE-CODE': _.lowerCase(STATE.code),
      Accept: 'application/json, */*',
      'X-LANGUAGE': 'en'
    }
  };

  return async (args, api, extraOptions = {}) => {
    const endpoint = _.get(args, 'url', args);
    const responseType = _.get(args, 'responseType', RESPONSE_TYPE.JSON);
    const getBaseUrl = () => {
      if ([API_URL.SAMPLE.SIGN_PDF].includes(endpoint)) {
        return import.meta.env.VITE_BASE_URL;
      }
      if ([API_URL.OTP.SEND_AADHAAR_OTP, API_URL.OTP.VERIFY_OTP].includes(endpoint)) {
        return import.meta.env.VITE_SDC_API_URL;
      }
      return import.meta.env.VITE_API_URL;
    };

    const baseQuery = fetchBaseQuery({
      baseUrl: getBaseUrl(),
      prepareHeaders: (headers, { getState }) => {
        const token = localStorage.getItem('token') || getState()?.auth?.token || '';
        if (token) headers.set('Authorization', `Bearer ${token}`);
        Object.entries(apiConfigs.headers).forEach(([key, value]) => {
          headers.set(key, value);
        });
        return headers;
      },
      responseHandler: async (response) => {
        const contentType = response.headers?.get?.('content-type') || '';
        const { ok: isSuccess } = response || {};

        const parseResponse = async () => {
          switch (responseType) {
            case RESPONSE_TYPE.FILE_STREAM:
            case RESPONSE_TYPE.BLOB:
            case RESPONSE_TYPE.FILE_BYTE:
              return isSuccess ? response.blob() : response.json();
            case RESPONSE_TYPE.ARRAY_BUFFER:
              return isSuccess ? response.arrayBuffer() : response.json();
            case RESPONSE_TYPE.TEXT:
              return isSuccess ? response.text() : response.json();
            case RESPONSE_TYPE.JSON:
              return response.json();
            default:
              if (contentType.includes('application/json')) {
                return response.json();
              }
              return response.text();
          }
        };
        const parsedData = await parseResponse();
        return parsedData;
      }
    });

    return baseQuery(args, api, extraOptions);
  };
};
