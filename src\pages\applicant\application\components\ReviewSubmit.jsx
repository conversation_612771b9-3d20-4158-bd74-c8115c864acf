import React, { useState, useEffect } from 'react';
import {
  Box, Text, VStack, DocumentPreviewSection
} from 'common/components';
import {
  AccordionComponent, PreviewSection, StepperButtons, Alert as CustomAlert
} from 'common/custom-components';
import { t } from 'i18next';
import { useSelector } from 'react-redux';
import { getFileInfo, getScholarshipType } from 'pages/common/selectors';
import colors from 'theme/foundations/colors';
import { useDownloadFileMutation } from 'pages/others/fileDownload/api';
import { API_URL } from 'common';
import { RESPONSE_TYPE } from 'pages/others/fileDownload/constant';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData,
  getDocumentDetails
} from 'pages/common/helpers';
import { APPLICATION_STATUS } from 'pages/common/constant';
import { STEPPER_STEPS } from '../constants';
import { getApplicationId } from '../selectors';
import { useSubmitApplicationMutation } from '../api';

const ReviewSubmit = ({
  onPrevious, onEdit, applicationDetails, isDetailsSuccess
}) => {
  const scholarshipType = useSelector(getScholarshipType);

  const [submitApplication, { isLoading: isSubmitting }] = useSubmitApplicationMutation();
  const [downloadFile, { data = {} }] = useDownloadFileMutation();
  const applicationId = useSelector(getApplicationId);
  const { applicationStatus = '' } = useSelector(getFileInfo);

  const [showAlert, setShowAlert] = useState(false);

  const getScholarshipTypeName = () => {
    if (scholarshipType) {
      return t(scholarshipType);
    }
    return null;
  };

  const getConfirmationMessage = () => {
    const scholarshipName = getScholarshipTypeName();
    return t('submitApplicationConfirmMessage', { scholarshipType: scholarshipName });
  };

  const getAcademicDetailsData = () => {
    return [
      ...getPreviousAcademicDetailsData(applicationDetails, isDetailsSuccess),
      ...getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess)
    ];
  };

  const handleEdit = (section) => {
    if (onEdit) onEdit(section);
  };

  const handleSubmitClick = () => {
    if (applicationStatus && applicationStatus !== APPLICATION_STATUS.DRAFT) return;
    setShowAlert(true);
  };

  const handleAlertClose = () => {
    setShowAlert(false);
  };

  const handleSubmitConfirm = () => {
    submitApplication({
      applicationId,
      closeAlert: () => setShowAlert(false)
    });
  };

  useEffect(() => {
    if (applicationId) {
      downloadFile({
        url: API_URL.APPLICATION.GET_DOCUMENTS.replace('{id}', applicationId),
        responseType: RESPONSE_TYPE.JSON
      });
    }
  }, [applicationId]);

  // Accordion data with preview sections
  const accordionData = [
    ...(applicationDetails?.personalDetails ? [{
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails?.personalDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      onClick: () => handleEdit(STEPPER_STEPS.APPLICANT_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      onClick: () => handleEdit(STEPPER_STEPS.PARENT_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      onClick: () => handleEdit(STEPPER_STEPS.BANK_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      onClick: () => handleEdit(STEPPER_STEPS.ACADEMIC_DETAILS),
      isCompleted: false
    }] : []),
    ...(getDocumentDetails(data) ? [{
      title: t('documentUpload'),
      content: (
        <DocumentPreviewSection documents={getDocumentDetails(data)} />
      ),
      id: 5,
      onClick: () => handleEdit(STEPPER_STEPS.DOCUMENTS_UPLOAD),
      isCompleted: false
    }] : [])
  ];

  return (
    <VStack spacing={4} align="stretch">
      {/* Header */}
      <Box alignContent="center" maxWidth={{ base: 'sm', md: 'md', lg: 'lg' }} mx="auto">
        <Text
          fontSize={{ base: 'sm', md: 'lg' }}
          fontWeight="semibold"
          color="white"
          alignItems="center"
          textAlign="center"
          p={2}
          borderRadius="md"
          bg={colors.primary[500]}
        >
          {t('reviewSubmit')}
        </Text>
      </Box>

      {/* Accordion with Preview Sections */}
      <AccordionComponent
        data={accordionData}
        allowMultiple
        currentIndexes={accordionData.map((_, index) => index)}
        isCollapsible={false}
      />

      {/* Action Buttons */}
      <StepperButtons
        currentStep={5}
        totalSteps={6}
        onNext={handleSubmitClick}
        onPrevious={onPrevious}
        submitButtonProps={{
          isDisabled: applicationStatus && applicationStatus !== APPLICATION_STATUS.DRAFT,
          ...(applicationStatus !== APPLICATION_STATUS.DRAFT ? { label: applicationStatus } : {})
        }}
        layout="space-between"
      />

      {/* Submit Confirmation Alert */}
      <CustomAlert
        open={showAlert}
        close={handleAlertClose}
        variant="success"
        bodyTitle={t('confirmApplication')}
        message={getConfirmationMessage()}
        forwardActionText={t('confirm')}
        backwardActionText={t('cancel')}
        actionForward={handleSubmitConfirm}
        actionBackward={handleAlertClose}
        actionForwardLoading={isSubmitting}
        closeOnOverlayClick={false}
        closeOnEsc={false}
      />
    </VStack>
  );
};

export default ReviewSubmit;
