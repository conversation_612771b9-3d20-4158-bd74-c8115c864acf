import { t } from 'common/components';
import * as yup from 'yup';
import { _ } from 'utils/lodash';
import { FILE_SIZE, VALID_FILE_TYPE } from 'pages/common/constant';
import { translateKey } from 'utils/translate';
import { DOCUMENT_META_DATA } from '../constants';

const fileTypeTest = (label) => yup.mixed()
  .test('fileFormat', t('invalidFileFormat'), (value) => {
    if (!value) return true;
    if (value instanceof File) {
      return VALID_FILE_TYPE.includes(value.type);
    }
    return true;
  })
  .test('file-required', t('fieldRequired', { field: translateKey(label) }), (value) => {
    if (!value) return false;
    if (value instanceof File) return true;
    return typeof value === 'object' && Object.keys(value).length > 0;
  })
  .test('fileSize', t('fileSizeExceeded'), (value) => {
    if (!value) return true;
    if (value instanceof File) return value.size <= FILE_SIZE;
    return true;
  });

export const documentSchema = (attachments = []) => {
  const shape = {};
  if (attachments.length > 0) {
    attachments.forEach((key) => {
      const { name: fieldName = '' } = _.get(DOCUMENT_META_DATA, key, {});
      if (fieldName) {
        shape[fieldName] = yup.mixed().concat(fileTypeTest(fieldName)).nullable();
      }
    });

    return yup.object().shape(shape);
  } return yup.object().shape({});
};
