import { ApplicationNoIcon, ScholarshipTypeIcon, UserIcon } from 'assets/svg';
import { API_URL } from 'common';
import {
  Badge, Box, CircularLoader, DocumentPreviewSection, HStack, Icon, Text
} from 'common/components';
import {
  AccordionComponent, BannerBox, PreviewSection
} from 'common/custom-components';
import { ROUTE_URL } from 'common/routeUrls';
import { t } from 'i18next';
import {
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getDocumentDetails,
  getParentGuardianDetailsData,
  getPersonalDetailsData,
  getPreviousAcademicDetailsData
} from 'pages/common/helpers';
import { actions as commonActions } from 'pages/common/slice';
import { useDownloadFileMutation } from 'pages/others/fileDownload/api';
import { RESPONSE_TYPE } from 'pages/others/fileDownload/constant';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { _ } from 'utils/lodash';
import { getStatusColor } from 'utils/common';
import { useGetApplicationDetailsQuery } from '../api';

const ApplicationView = () => {
  const { applicationId } = useParams();
  const dispatch = useDispatch();

  const [downloadFile, { data = {} }] = useDownloadFileMutation();
  const {
    data: { payload: applicationDetails = {} } = {},
    isLoading: isDetailsLoading,
    isSuccess: isDetailsSuccess,
    isError: isDetailsError
  } = useGetApplicationDetailsQuery(applicationId, {
    skip: !applicationId,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true
  });

  useEffect(() => {
    if (applicationId) {
      downloadFile({
        url: API_URL.APPLICATION.GET_DOCUMENTS.replace('{id}', applicationId),
        responseType: RESPONSE_TYPE.JSON
      });
    }
  }, [applicationId]);

  // Redirect to dashboard on error or invalid data
  useEffect(() => {
    const shouldRedirect = () => {
      if (isDetailsError) return true;

      if (isDetailsSuccess) {
        if (_.isEmpty(applicationDetails)) return true;

        const { personalDetails = {} } = applicationDetails;
        if (_.isEmpty(personalDetails)) return true;
      }

      return false;
    };

    if (shouldRedirect()) {
      dispatch(
        commonActions.navigateTo({
          to: `ui/${ROUTE_URL.APPLICANT.BASE.DASHBOARD}`,
          replace: true
        })
      );
    }
  }, [isDetailsSuccess, isDetailsError, applicationDetails, dispatch]);

  const {
    firstName, lastName, scholarshipType, applicationStatus, applicationNumber
  } = applicationDetails?.personalDetails || {};

  // Desktop content for BannerBox
  const desktopContent = (
    <>
      <HStack spacing={4} mb={2}>
        <Icon as={UserIcon} boxSize={5} />
        <Text fontSize="md" color="white">
          <Text as="span" fontWeight="normal">{t('studentName')} : </Text>
          <Text as="span" fontWeight="semibold">{firstName} {lastName}</Text>
        </Text>
      </HStack>

      <HStack spacing={4} mb={2}>
        <Icon as={ScholarshipTypeIcon} boxSize={5} />
        <Text fontSize="md" color="white">
          <Text as="span" fontWeight="normal">{t('scholarshipType')} : </Text>
          <Text as="span" fontWeight="semibold">{scholarshipType || 'Higher Secondary (HSS)'}</Text>
        </Text>
      </HStack>

      <HStack spacing={4}>
        <Icon as={ApplicationNoIcon} boxSize={5} />
        <Text fontSize="md" color="white">
          <Text as="span" fontWeight="normal">{t('applicationNumber')} / {t('status')} : </Text>
          <Text as="span" fontWeight="semibold">{applicationNumber || 'NIL'}</Text>
          <Text as="span" mx={2}>/</Text>
          <Badge
            px={4}
            py={1}
            borderRadius="full"
            bg={getStatusColor(applicationStatus)}
            fontSize="sm"
            fontWeight="semibold"
          >
            {applicationStatus || 'NIL'}
          </Badge>
        </Text>
      </HStack>
    </>
  );

  // Application info header
  const renderApplicationInfo = () => (
    <BannerBox
      mobileContent={desktopContent}
      desktopContent={desktopContent}
      showImages
    />
  );

  // Combine academic details for display
  const getAcademicDetailsData = () => {
    const currentCourseData = getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess);
    const previousAcademicData = getPreviousAcademicDetailsData(
      applicationDetails,
      isDetailsSuccess
    );
    return [...currentCourseData, ...previousAcademicData];
  };

  // Accordion data with preview sections
  const accordionData = [
    ...(applicationDetails?.personalDetails ? [{
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails?.personalDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      isCompleted: false
    }] : []),
    ...(getDocumentDetails(data) ? [{
      title: t('documentUpload'),
      content: (
        <DocumentPreviewSection documents={getDocumentDetails(data)} />
      ),
      id: 5,
      isCompleted: false
    }] : [])
  ];

  // Show loading state
  if (isDetailsLoading) {
    return (
      <CircularLoader
        message={t('loadingApplicationDetails')}
        fullPage
      />
    );
  }

  return (
    <>
      {/* Application info */}
      { isDetailsSuccess && renderApplicationInfo()}

      <Box minH="auto">
        {/* Content Sections */}
        {isDetailsSuccess && (
          <AccordionComponent
            data={accordionData}
            allowMultiple
            currentIndexes={Array.from(Array(accordionData.length).keys())}
            isCollapsible={false}
          />
        )}
      </Box>
    </>
  );
};

export default ApplicationView;
